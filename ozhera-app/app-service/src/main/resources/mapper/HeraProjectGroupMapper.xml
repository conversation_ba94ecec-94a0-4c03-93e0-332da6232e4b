<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.ozhera.app.dao.mapper.HeraProjectGroupMapper">
  <resultMap id="BaseResultMap" type="org.apache.ozhera.app.api.model.project.group.HeraProjectGroupModel">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="relation_object_id" jdbcType="INTEGER" property="relationObjectId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="cn_name" jdbcType="VARCHAR" property="cnName" />
    <result column="parent_group_id" jdbcType="INTEGER" property="parentGroupId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, type, relation_object_id, name, cn_name, parent_group_id, status, level, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="org.apache.ozhera.app.model.HeraProjectGroupExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from hera_project_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null and limit >= 0">
      limit #{offset} , #{limit}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from hera_project_group
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from hera_project_group
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="org.apache.ozhera.app.model.HeraProjectGroupExample">
    delete from hera_project_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="org.apache.ozhera.app.model.HeraProjectGroup">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into hera_project_group (type, relation_object_id, name, 
      cn_name, parent_group_id, status, 
      level, create_time, update_time
      )
    values (#{type,jdbcType=INTEGER}, #{relationObjectId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, 
      #{cnName,jdbcType=VARCHAR}, #{parentGroupId,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{level,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="org.apache.ozhera.app.model.HeraProjectGroup">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into hera_project_group
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        type,
      </if>
      <if test="relationObjectId != null">
        relation_object_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="cnName != null">
        cn_name,
      </if>
      <if test="parentGroupId != null">
        parent_group_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="relationObjectId != null">
        #{relationObjectId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="cnName != null">
        #{cnName,jdbcType=VARCHAR},
      </if>
      <if test="parentGroupId != null">
        #{parentGroupId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="level != null">
        #{level,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="org.apache.ozhera.app.model.HeraProjectGroupExample" resultType="java.lang.Long">
    select count(*) from hera_project_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update hera_project_group
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.relationObjectId != null">
        relation_object_id = #{record.relationObjectId,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.cnName != null">
        cn_name = #{record.cnName,jdbcType=VARCHAR},
      </if>
      <if test="record.parentGroupId != null">
        parent_group_id = #{record.parentGroupId,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.level != null">
        level = #{record.level,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update hera_project_group
    set id = #{record.id,jdbcType=INTEGER},
      type = #{record.type,jdbcType=INTEGER},
      relation_object_id = #{record.relationObjectId,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      cn_name = #{record.cnName,jdbcType=VARCHAR},
      parent_group_id = #{record.parentGroupId,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      level = #{record.level,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="org.apache.ozhera.app.model.HeraProjectGroup">
    update hera_project_group
    <set>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="relationObjectId != null">
        relation_object_id = #{relationObjectId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="cnName != null">
        cn_name = #{cnName,jdbcType=VARCHAR},
      </if>
      <if test="parentGroupId != null">
        parent_group_id = #{parentGroupId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="level != null">
        level = #{level,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.apache.ozhera.app.model.HeraProjectGroup">
    update hera_project_group
    set type = #{type,jdbcType=INTEGER},
      relation_object_id = #{relationObjectId,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      cn_name = #{cnName,jdbcType=VARCHAR},
      parent_group_id = #{parentGroupId,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      level = #{level,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into hera_project_group
    (type, relation_object_id, name, cn_name, parent_group_id, status, level, create_time, 
      update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.type,jdbcType=INTEGER}, #{item.relationObjectId,jdbcType=INTEGER}, #{item.name,jdbcType=VARCHAR}, 
        #{item.cnName,jdbcType=VARCHAR}, #{item.parentGroupId,jdbcType=INTEGER}, #{item.status,jdbcType=INTEGER}, 
        #{item.level,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into hera_project_group (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=INTEGER}
        </if>
        <if test="'relation_object_id'.toString() == column.value">
          #{item.relationObjectId,jdbcType=INTEGER}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'cn_name'.toString() == column.value">
          #{item.cnName,jdbcType=VARCHAR}
        </if>
        <if test="'parent_group_id'.toString() == column.value">
          #{item.parentGroupId,jdbcType=INTEGER}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'level'.toString() == column.value">
          #{item.level,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>