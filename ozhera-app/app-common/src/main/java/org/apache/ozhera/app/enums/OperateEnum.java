/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.ozhera.app.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2021/7/16 16:45
 */
@Getter
public enum OperateEnum {

    ADD_OPERATE(1, "新增"),
    UPDATE_OPERATE(2, "修改"),
    DELETE_OPERATE(3, "删除");

    private final Integer code;
    private final String describe;

    OperateEnum(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public static OperateEnum queryByCode(Integer code) {
        return Arrays.stream(OperateEnum.values())
                .filter(operateEnum -> Objects.equals(operateEnum.getCode(), code))
                .findFirst()
                .orElse(null);
    }
}
