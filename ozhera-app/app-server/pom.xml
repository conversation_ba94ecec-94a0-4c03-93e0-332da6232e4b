<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.apache.ozhera</groupId>
        <artifactId>ozhera-app</artifactId>
        <version>2.2.6-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>app-server</artifactId>
    <version>2.2.6-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <!-- inner module -->
        <dependency>
            <groupId>org.apache.ozhera</groupId>
            <artifactId>app-service</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- inner module -->

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>infra-result</artifactId>
        </dependency>

        <!-- test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <version>2.7.15</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>


    <profiles>
        <profile>
            <id>apache-release</id>
            <properties>
                <profileActive>apache-release</profileActive>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <filters>
                    <filter>src/main/resources/application-open.properties</filter>
                </filters>

                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                    <resource>
                        <directory>${project.basedir}/../../</directory>
                        <filtering>true</filtering>
                        <includes>
                            <include>DISCLAIMER</include>
                            <include>NOTICE</include>
                            <include>LICENSE</include>
                        </includes>
                        <targetPath>META-INF/</targetPath>
                    </resource>
                </resources>

                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <version>2.7.15</version>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>integrated-deployment</id>
            <build>
                <filters>
                    <filter>src/main/resources/application-open.properties</filter>
                </filters>

                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                    <resource>
                        <directory>${project.basedir}/../../</directory>
                        <filtering>true</filtering>
                        <includes>
                            <include>DISCLAIMER</include>
                            <include>NOTICE</include>
                            <include>LICENSE</include>
                        </includes>
                        <targetPath>META-INF/</targetPath>
                    </resource>
                </resources>

                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <version>2.7.15</version>
                        <configuration>
                            <mainClass>org.apache.ozhera.app.AppBootstrap</mainClass>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>opensource-deploy</id>
            <properties>
                <profileActive>opensource-deploy</profileActive>
            </properties>
            <build>
                <filters>
                    <filter>src/main/resources/application-open.properties</filter>
                </filters>

                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <excludes>
                            <exclude>application*.properties</exclude>
                        </excludes>
                    </resource>
                </resources>
            </build>
        </profile>

    </profiles>

</project>