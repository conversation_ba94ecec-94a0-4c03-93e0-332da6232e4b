# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

spring.application.name=hear-app
server.port=@server.port@
server.type=@server.type@
log.path=@log.path@
logging.config=classpath:logback-spring.xml

mybatis-plus.mapper-locations=classpath:mapper/*.xml
mybatis-plus.type-aliases-package=org.apache.ozhera.app.model
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.global-config.db-config.id-type=auto
#mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

nacos.config.addrs=@nacos.config.addrs@
dubbo.group=@dubbo.group@
dubbo.protocol.id=@dubbo.protocol.id@
dubbo.protocol.name=@dubbo.protocol.name@
dubbo.protocol.port=@dubbo.protocol.port@

#rocket.mq.producer.group=@rocket.mq.producer.group@
rocket.mq.hera.app.topic=@rocket.mq.hera.app.topic@
rocket.mq.hera.app.tag=@rocket.mq.hera.app.tag@
rocket.mq.hera.ip.change.topic=hera_app_ip_change

job_start_flag=@job_start_flag@

app.ip.fetch.type=nacos

service.selector.property=outer