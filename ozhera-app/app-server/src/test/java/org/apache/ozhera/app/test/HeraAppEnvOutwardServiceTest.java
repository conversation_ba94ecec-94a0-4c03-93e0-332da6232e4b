/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.ozhera.app.test;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/2/28 12:12
 */
@Slf4j
//@SpringBootTest(classes = AppBootstrap.class)
public class HeraAppEnvOutwardServiceTest {

//    @Autowired
//    public HeraAppEnvOutwardService heraAppEnvOutwardService;
//
//    private Gson gson = new Gson();
//
//    @Test
//    public void queryEnvByIdTest() {
//        List<HeraAppEnvData> heraAppEnvData = heraAppEnvOutwardService.queryEnvById(null, null, 2L);
//        log.info("result:{}", gson.toJson(heraAppEnvData));
//    }

}
