<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->

---

name: Major Project Proposal

about: Use this template to report major proposal on the Apache OzHera(Incubating) project.

---

### Ⅰ. Proposal Summary

Provide a brief overview of the significant proposal or change in the project.

### Ⅱ. Details of the Proposal

Explain the proposal in detail. Include any relevant context or background information:

- What changed?
- Why was this update necessary?

### Ⅲ. Expected Impact

Describe the expected impact of this proposal, including but not limited to:

- Performance improvements
- API changes or additions
- New features or functionalities
- Changes to project dependencies

### Ⅳ. Implementation Details

Provide specific details on how the update was implemented:

1. Key steps or phases
2. Codebase changes (include links if possible)
3. Testing procedures or verification methods

### Ⅴ. Next Steps or Follow-Up Actions

List any further actions or next steps following this update, such as:

- Future improvements or adjustments
- Additional testing or validation needs
- Related updates or dependencies to track

### Ⅵ. Related Documentation

Attach any related documentation, guides, or links to further information for the community.