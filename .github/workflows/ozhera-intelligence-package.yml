# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# This is a basic workflow to help you get started with Actions

name: ozhera-intelligence test

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
on:
  pull_request:
    paths:
    - 'ozhera-intelligence/**'
  workflow_dispatch:
  
# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  check-build-21:
    name: ozhera-intelligence test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          java-version: 21
          distribution: 'zulu'
          cache: maven
          #settings-properties: '[{"propertyName1": "propertyValue1"}, {"propertyName2": "propertyValue2"}]'

      - run: echo '<settings>    <interactiveMode>false</interactiveMode>    <profiles>        <profile>            <repositories>                <repository>                    <snapshots />                    <id>ossrh</id>                    <name>ossrh-snapshot</name>                    <url>https://s01.oss.sonatype.org/content/repositories/snapshots</url>                </repository>            </repositories>            <pluginRepositories>                <pluginRepository>                    <snapshots />                    <id>ossrh</id>                    <name>ossrh-snapshot</name>                    <url>https://s01.oss.sonatype.org/content/repositories/snapshots</url>                </pluginRepository>            </pluginRepositories>            <id>artifactory</id>        </profile>    </profiles>    <activeProfiles>        <activeProfile>artifactory</activeProfile>    </activeProfiles>    <servers>        <server>            <id>github</id>            <username>${env.GITHUB_ACTOR}</username>            <password>${env.GITHUB_TOKEN}</password>        </server>    </servers>    <mirrors/>    <proxies/></settings>' > ~/.m2/settings.xml

      - run: cd ozhera-intelligence; mvn -U clean package
