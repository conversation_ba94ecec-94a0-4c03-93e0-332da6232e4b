# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# mone bootstrap Custom resources
# apply Install the entire mione system directly.
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: heras.k8s.mone.run
spec:
  group: k8s.mone.run
  versions:
    - name: v1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            spec:
              type: object
              properties:
                resourceList:
                  type: array
                  items:
                    type: object
                    properties:
                      needCreate:
                        type: boolean
                      required:
                        type: boolean
                      resourceType:
                        type: string
                      yamlStr:
                        type: string
                      defaultYamlPath:
                        type: string
                      remark:
                        type: string
                      connectionMapList:
                        type: array
                        items:
                          type: object
                          properties:
                            key:
                              type: string
                            value:
                              type: string
                            remark:
                              type: string
                            required:
                              type: string
                      defaultExtendConfigPath:
                        type: array
                        items:
                          type: string
                      propList:
                        type: array
                        items:
                          type: object
                          properties:
                            key:
                              type: string
                            value:
                              type: string
            status:
              type: object
              properties:
                status:
                  type: integer
                msg:
                  type: string
      subresources:
        status: { }
  names:
    kind: HeraBootstrap
    plural: heras
    singular: hera
    shortNames:
      - mb
  scope: Namespaced
