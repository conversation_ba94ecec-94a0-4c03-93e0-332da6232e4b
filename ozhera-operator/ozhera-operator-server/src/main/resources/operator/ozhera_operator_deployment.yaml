# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
apiVersion: v1
kind: Service
metadata:
  name: hera-op-srv
  namespace: ozhera-namespace
  labels:
    app: hera-operator-server
spec:
  type: NodePort
  ports:
    - protocol: TCP
      name: hera-operator-port
      port: 8998
      targetPort: hera-8998
      nodePort: 30998
  selector:
    app: hera-operator
---
apiVersion: v1
kind: Service
metadata:
  name: hera-op-nginx
  namespace: ozhera-namespace
spec:
  ports:
    - port: 80
      targetPort: 7001
      protocol: TCP
  selector:
    app-fe: hera-operator-fe
  type: LoadBalancer
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: hera-operator-deployment
  namespace: ozhera-namespace
spec:
  selector:
    matchLabels:
      app: hera-operator
  replicas: 1
  template:
    metadata:
      labels:
        app: hera-operator
        app-fe: hera-operator-fe
    spec:
      imagePullSecrets:
      - name: mione-opensource-credential
      containers:
        - name: hera-operator
          image: herahub/opensource-pub:ozhera-operator-server-2.2.6-SNAPSHOT-beta-v1
          ports:
            - containerPort: 8998
              name: hera-8998
        - name: hera-operator-fe
          image: herahub/opensource-pub:hera-operator-fe-v3-release
          ports:
            - containerPort: 7001
              name: hera-7001
      serviceAccountName: admin-mone