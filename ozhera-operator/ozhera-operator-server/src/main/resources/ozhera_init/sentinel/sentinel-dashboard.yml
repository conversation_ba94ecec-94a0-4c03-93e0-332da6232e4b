# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sentinel-dashboard
  namespace: ozhera-namespace
  labels:
    app: sentinel-dashboard
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sentinel-dashboard
  template:
    metadata:
      labels:
        app: sentinel-dashboard
    spec:
      containers:
        - name: sentinel-dashboard-container
          image: herahub/opensource-pub:sentinel-dashboard-v1.1
          volumeMounts:
            - name: log-path
              mountPath: /home/<USER>/log
          resources:
            limits:
              cpu: '500m'
              memory: 2Gi
      volumes:
        - name: log-path
          emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: sentinel-dashboard
  namespace: ozhera-namespace
  labels:
    app: sentinel-dashboard
spec:
  ports:
    - port: 8088
      targetPort: 8088
      protocol: TCP
  selector:
    app: sentinel-dashboard
  clusterIP: None
