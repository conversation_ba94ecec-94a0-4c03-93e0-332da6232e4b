# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
kind: DaemonSet
apiVersion: apps/v1
metadata:
  name: cadvisor
  namespace: ozhera-namespace
spec:
  selector:
    matchLabels:
      app: cadvisor
      release: cadvisor
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: cadvisor
        release: cadvisor
    spec:
      volumes:
        - name: rootfs
          hostPath:
            path: /
            type: ''
        - name: varrun
          hostPath:
            path: /var/run
            type: ''
        - name: sys
          hostPath:
            path: /sys
            type: ''
        - name: docker
          hostPath:
            path: /var/lib/docker
            type: ''
        - name: disk
          hostPath:
            path: /dev/disk
            type: ''
      containers:
        - name: cadvisor
          image: herahub/opensource-pub:cadvisor-v1-release
          args:
            - '--port=5195'
            - '--housekeeping_interval=10s'
            - '--max_housekeeping_interval=15s'
            - '--event_storage_event_limit=default=0'
            - '--event_storage_age_limit=default=0'
            - '--enable_metrics=cpuLoad,advtcp,tcp,network,app,cpu,disk,diskIO,memory,process'
            - '--store_container_labels=false'
            - '--docker_only'
            - '--whitelisted_container_labels=io.kubernetes.container.name,io.kubernetes.pod.name,io.kubernetes.pod.namespace,annotation.io.kubernetes.container.restartCount'
            - '--env_metadata_whitelist=ENV_ID,PROJECT_ID,application,serverEnv,POD_IP'
            - '--enable_load_reader=true'
          ports:
            - name: http
              hostPort: 5195
              containerPort: 5195
              protocol: TCP
          resources:
            requests:
              cpu: 300m
              memory: 200Mi
          volumeMounts:
            - name: rootfs
              readOnly: true
              mountPath: /rootfs
            - name: varrun
              readOnly: true
              mountPath: /var/run
            - name: sys
              readOnly: true
              mountPath: /sys
            - name: docker
              readOnly: true
              mountPath: /var/lib/docker
            - name: disk
              readOnly: true
              mountPath: /dev/disk
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      automountServiceAccountToken: false
      hostNetwork: true
      securityContext: {}
      schedulerName: default-scheduler
      tolerations:
        - operator: Exists
      priorityClassName: system-cluster-critical
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 0
  revisionHistoryLimit: 10