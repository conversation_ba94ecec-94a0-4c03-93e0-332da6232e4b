# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mi-tcplogin
  namespace: ozhera-namespace
  labels:
    app: mi-tcplogin
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mi-tcplogin
  template:
    metadata:
      labels:
        app: mi-tcplogin
    spec:
      containers:
      - name: mi-tcplogin-container
        image: herahub/opensource-pub:mi-tpclogin-v2-release
        resources:
          limits:
            cpu: '500m'
            memory: 2Gi
---
apiVersion: v1
kind: Service
metadata:
  name: mi-tpclogin
  namespace: ozhera-namespace
  labels:
    app: mi-tpclogin
spec:
  ports:
    - port: 8098
      targetPort: 8098
      protocol: TCP
  selector:
    app: mi-tcplogin
  clusterIP: None