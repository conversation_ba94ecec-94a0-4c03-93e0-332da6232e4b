# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
apiVersion: apps/v1
kind: Deployment
metadata:
  name: hera-demo-client
  namespace: ozhera-namespace
  labels:
    app: hera-demo-client
spec:
  replicas: 1
  selector:
    matchLabels:
      app: hera-demo-client
  template:
    metadata:
      labels:
        app: hera-demo-client
    spec:
      containers:
      - name: hera-demo-client-container
        image: herahub/opensource-pub:ozhera-demo-client-server-2.2.6-SNAPSHOT-beta-v1
        env:
          - name: MIONE_PROJECT_ENV_NAME
            value: dev
          - name: MIONE_PROJECT_ENV_ID
            value: '2'
          - name: MIONE_PROJECT_NAME
            value: 2-hera-demo-client
        volumeMounts:
          - name: log-path
            mountPath: /home/<USER>/log
        resources:
          limits:
            cpu: '500m'
            memory: 2Gi

      - name: log-agent
        image: herahub/opensource-pub:log-agent-server-2.2.6-SNAPSHOT-beta-v1
        volumeMounts:
        - mountPath: "/home/<USER>/log"
          name: log-path
        resources:
          limits:
            cpu: '300m'
            memory: 1Gi
      volumes:
      - name: log-path
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: hera-demo-client
  namespace: ozhera-namespace
  labels:
    app: hera-demo-client
spec:
  ports:
    - port: 8085
      targetPort: 8085
      protocol: TCP
  selector:
    app: hera-demo-client
  clusterIP: None