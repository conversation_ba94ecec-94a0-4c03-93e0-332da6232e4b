# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ozhera-intelligence
  namespace: ozhera-namespace
  labels:
    app: ozhera-intelligence
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ozhera-intelligence
  template:
    metadata:
      labels:
        app: ozhera-intelligence
    spec:
      containers:
      - name: ozhera-intelligence-container
        image: herahub/opensource-pub:ozhera-intelligence-2.2.5-incubating-beta-v1
        imagePullPolicy: Always
        ports:
        - containerPort: 4446
        resources:
          limits:
            cpu: '500m'
            memory: 1Gi
