# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#server
dubbo.group=opensource-outer
dubbo.protocol.id=dubbo
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.registry.address=nacos://${hera.nacos.address}
nacos.username=${hera.nacos.username}
nacos.password=${hera.nacos.password}

youpin.log.group=opensource-outer
log.path=/home/<USER>/log

mione.k8s.container.port=5195
mione.k8s.node.port=9101

# db conf
spring.datasource.url=jdbc:mariadb://${hera.datasource.url}/hera?characterEncoding=utf8&useSSL=false
spring.datasource.username=${hera.datasource.username}
spring.datasource.password=${hera.datasource.password}
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.default.initialPoolSize=10
spring.datasource.default.maxPoolSize=20
spring.datasource.default.minialPoolSize=10

#prometheus
job.prometheus.enabled=true
job.prometheus.filePath=/prometheus/prometheus.yml
job.prometheus.healthAddr=http://${hera.prometheus.url}/-/healthy
job.prometheus.reloadAddr=http://${hera.prometheus.url}/-/reload
job.prometheus.Addr=http://${hera.prometheus.url}

#alertManager
job.alertManager.enabled=true
job.alertManager.filePath=/prometheus/alert.rules
job.alertManager.reloadAddr=http://${hera.prometheus.url}/-/reload
job.alertManager.Addr=http://${hera.alertmanager.url}

jaeger_query_token=adqSWsad19E8D4

is_k8s_deploy=false

feishu.appid=xxxxxx
feishu.appSecret=

hera.alertmanager.url=${hera.alertmanager.url}
hera.alert.type=feishu

# update your public email server address
mail.user.name=<EMAIL>
mail.pwd.code=xxxxxxx
mail.host=smtp.163.com
mail.smtp.auth=true
mail.smtp.port=25

dingding.appKey=xxx
dingding.appSecret=xxx
dingding.robotCode=xxx
dingding.callbackUrl=http://xxxx/api/v1/silence

hera.app.addr=http://hera-app
golang.runtime.default.port=2223

dingding.user.type=userId
hera.alert.whiteList=

# prometheus type
prometheus-agent.client.type=ali
#ali prometheus
prometheus.ali.accessKeyId=xxx
prometheus.ali.accessKeySecret=xxxx
prometheus.ali.cluster.id=xxx
prometheus.ali.resourceId=xxxx
prometheus.ali.webhook.url=xxxx
prometheus.ali.webhook.method=Post

#vm cluster
vm.agent.label=ozhera-vmagent
vm.agent.port=8429
vm.Alert.label=ozhera-vmalert
vm.Alert.Port=8880
vm.Insert.label=ozhera-vminsert
vm.Insert.Port=8480
vmSelect.label=ozhera-vmselect
vm.Select.Port=8481
vmStorage.label=ozhera-vmstorage
vm.Storage.Port=8482
