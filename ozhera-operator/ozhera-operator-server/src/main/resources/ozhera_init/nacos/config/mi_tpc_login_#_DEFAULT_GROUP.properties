# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#0??(redis???????)?1redis
cache.type=1
redis.address=${hera.redis.url}
redis.pwd=${hera.redis.password}
redis.max-active=24
redis.max-wait=500
redis.max-idle=8
redis.min-idle=0
redis.timeout=1000
redis.cluster=no
#mysql??
datasource.url=jdbc:mysql://${hera.datasource.url}/mi_tpc?characterEncoding=utf8&useSSL=false&autoReconnect=true&autoReconnectForPools=true
datasource.username=${hera.datasource.username}
datasource.pwd=${hera.datasource.password}
datasource.min-idle=10
datasource.max-pool-size=20
datasource.idle-timeout=60000
datasource.pool-name=hikar-mysql

home.url=http://${tpc.login.fe.url}

github.client_id=Iv1.413c5ee8795dc2df
github.client_secret=6e54cfc732e11e96a36c5d7576d5476c80b39f09
gitlab.client_id=c989eaaf22e8f0a01cc0414c8ac8fe381511f00182947720dfd7eac64c509923
gitlab.client_secret=44a77f2830cdb5bc7c11316906382ab13274679f1607bbe9a3bd8f8139be7aaa

feishu.client_id=cli_a4260627e3fb900d
feishu.client_secret=waPQcTVSaNsxYRWXtVSHybVxLt5IuZaU
feishu.oauth.auth.url=https://passport.feishu.cn/suite/passport/oauth/authorize
feishu.oauth.token.url=https://passport.feishu.cn/suite/passport/oauth/token
feishu.oauth.user.url=https://passport.feishu.cn/suite/passport/oauth/userinfo

dingding.client_id=ding796xb9llmrbtfbrb
dingding.client_secret=Ztv9tdXRca79-7R4Gcb7gL-CujfqcmYoybgkjaKaX1w_jCtG3bIjTz_wMK1ENo_M
dingding.oauth.auth.url=https://login.dingtalk.com/oauth2/auth
dingding.oauth.token.url=https://api.dingtalk.com/v1.0/oauth2/userAccessToken
dingding.oauth.user.url=https://api.dingtalk.com/v1.0/contact/users/me
