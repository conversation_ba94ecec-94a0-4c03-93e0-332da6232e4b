# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
nacos.username=nacos
nacos.password=nacos
tpc.url=http://mi-tpc:8097/backend/node/inner_list
tpc.token=xxx
tpc.pageSize=100
# application need
app.key.name=app
# label or env
app.key.type=label
# serverEnv need
env.key.name=serverEnv
# label or env
env.key.type=label
webhook.namespace=ozhera-namespace
# Which namespaces need to be automatically injected with log-agent ,Support, segmentation
log.agent.condition.namespace=ozhera-namespace
log.agent.volume.mount.name=log-path
# log-agent Specifies the pod prefix supported by log-agent Support, segmentation
log.agent.pod.prefix=hera-demo,otel-go
log.agent.container.name=log-agent
log.agent.container.image=herahub/opensource-pub:log-agent-v1.2.8
log.agent.container.cpu.limit=1
log.agent.container.mem.limit=2Gi
log.agent.nacos.addr=nacos:80
# Whether to enable automatic log-agent injection
log-agent.enabled=true