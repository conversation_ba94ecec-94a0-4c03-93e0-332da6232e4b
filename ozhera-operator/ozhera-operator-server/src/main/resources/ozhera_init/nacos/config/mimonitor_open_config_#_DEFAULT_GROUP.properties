# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# mysql数据源配置
spring.datasource.url=jdbc:mariadb://${hera.datasource.url}/hera?characterEncoding=utf8&useSSL=false
spring.datasource.username=${hera.datasource.username}
spring.datasource.password=${hera.datasource.password}
spring.datasource.driverClassName=com.mysql.jdbc.Driver
spring.datasource.default.initialPoolSize=10
spring.datasource.default.maxPoolSize=20
spring.datasource.default.minialPoolSize=10

# rocket mq
rocketmq.ak=${hera.rocketmq.ak}
rocketmq.sk=${hera.rocketmq.sk}
rocketmq.namesrv.addr=${hera.rocketmq.nameserver}
rocketmq.topic.hera.app=hera_app_operate
rocketmq.tag.hera.app=app_modify
rocketmq.group.hera.app=hera_app_mimonitor

hera.app.modify.notice.topic=hera_app_modify_notice_open
hera.app.modify.notice.tag=hera_app_modify_notice_tag_open
hera.app.modify.notice.group=mimonitor_open

# grafana 看板跳转域名
grafana.domain=http://${hera.grafana.url}

# prometheus
prometheus.url=http://${hera.prometheus.url}/
prometheus.check.url=http://${hera.prometheus.url}/
prometheus.alarm.env=staging

#报警扫描间隔，单位s
rule.evaluation.interval=20

#报警扫描时间区间，单位s
rule.evaluation.duration=30

#报警扫描单位，单位s
rule.evaluation.unit=s

#黑白名单，可不写value
hera.access.member.white.list=xxx
hera.access.member.black.list=xxx
hera.access.dept.black.list=xxx

#管理员列表，可不写value
hera.admin.member.list=xxx

#指定访问用户（可不填value）
assign.login.user=xxx

#资源利用率页面url配置（给前端装载页面使用的url）
resource.use.rate.url=http://${hera.grafana.url}/d/Q83uxVPnz/zi-yuan-shi-yong-lu-da-pan?orgId=1

#资源使用率无数据页面配置（给前端装载页面使用的url）
resource.use.rate.nodata.url=http://${hera.grafana.url}/d/jCTskW9nz/no-data?orgId=1

#资源使用率阈值配置(%),小于该值推送消息
resource.use.rate.alarm.config=10

#k8s 扩容巡检时间间隔
k8s.capacity.adjust.check.interval=3

# feishu
feishu.monitor.appId=xxx
feishu.monitor.appSecret=xxx

# es
es.address=${hera.es.url}
es.username=${hera.es.username}
es.password=${hera.es.password}
esIndex=zgq_common_staging_private_prometheus
es.middleware.info.index=mione-staging-zgq-driver
es.query.timeout=3000

# token解析url
token.parse.url=http://mi-tpclogin:8098/login/token/parse

# 登陆首页url
login.url=http://${tpc.login.fe.url}/user-manage/login

# 基础报警跳转url
hera.url=http://${hera.homepage.url}/project-target-monitor/application/dash-board

alert.manager.env=openSource
scrape.job=openSource

grafana.api.key=eyJrIjoiUUJucU9veVRYQXhuUlZ4V2NzVXpaNHhkdVJGbWVXdTYiLCJuIjoiYWRtaW4iLCJpZCI6MX0=
grafana.apikey.url=/api/auth/keys
grafana.prometheus.datasource=Prometheus
grafana.address=http://grafana
grafana.createDashboard.url=/api/dashboards/db
grafana.checkDashboard.url=/api/dashboards/uid/
grafana.version.url=/api/dashboards/id/{dashboard_id}/versions?limit=1
grafana.folder.id=1
grafana.folder.uid=Hera
grafana.folder.url=/api/folders
grafana.container.url=/d/hera-docker-monitor/herarong-qi-jian-kong?orgId=1&refresh=5s&var-Node=
grafana.host.url=/d/hera-node-monitor/herawu-li-ji-jian-kong?orgId=1&var-node=
grafana.datasource.url=/api/datasources
grafana.jaeger.query.token=adqSWsad19E8D4
grafana.backend.users=xx
prometheusUid=f6Lhk8A4z
grafana.username=xxx
grafana.password=xxx

hera.dash.url=http://${hera.homepage.url}/project-target-monitor/application/dash-board
cn.grafana.url=http://${hera.grafana.url}/d/tFjrMVeGz/zhong-guo-qu-staging-rong-qi-jian-kong
cn.grafana.disk_rate.url=http://${hera.grafana.url}/d/lMrNAQN4k/mione-k8s-gen-mu-lu-ci-pan-shi-yong-liang-da-pan
