# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
db_driver=com.mysql.cj.jdbc.Driver
db_url=jdbc:mysql://${hera.datasource.url}/hera?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowPublicKeyRetrieval=true
db_user_name=${hera.datasource.username}
db_pwd=${hera.datasource.password}

auth_token_url=http://mi-tpclogin:8098/login/token/parse
tpc_login_url=http://${tpc.login.fe.url}/user-manage/login
tpc_logout_url=http://${tpc.login.fe.url}/api-pomission/login/logout

rocketmq_namesrv_addr=${hera.rocketmq.nameserver}
rocketmq_ak=${hera.rocketmq.ak}
rocketmq_sk=${hera.rocketmq.sk}

tpc_home_url_head=http://${hera.tpc.url}