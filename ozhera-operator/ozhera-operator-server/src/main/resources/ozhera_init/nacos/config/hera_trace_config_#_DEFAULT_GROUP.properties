# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
query.env=staging
query.excludeMethod=favicon.ico|ResourceHttpRequestHandler|ResponseFacade|Controller|Render|/*|EXISTS|PING
query.exclude.httpServer=PUT|HTTP
query.excludeThread=nacos
query.excludeDB=select ?|/|@@
query.excludeHttpurl=nacos|talos
query.excludeUA=SecurityScan|skyeye-scanner|wcscanner
query.excludeMetrics=
query.dispatcher.excludeServiceName=jaeger-query|tesla
query.slowtime.http=1000
query.slowtime.dubbo=1000
query.slowtime.mysql=1000
trace.threshold=500
trace.es.filter.spanname=dbDriver
trace.es.filter.app.name=
trace.duration.threshold=1000
trace.es.filter.isopen=true
trace.es.filter.random.base=1000
hera.admin.member.list=
dubbo.registry.address=nacos://${hera.nacos.address}
nacos.config.addrs=${hera.nacos.address}
nacos.address=${hera.nacos.address}
es.trace.address=${hera.es.url}
es.trace.username=${hera.es.username}
es.trace.password=${hera.es.password}
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.url=jdbc:mysql://${hera.datasource.url}/hera?characterEncoding=utf8&useSSL=false
spring.datasource.username=${hera.datasource.username}
spring.datasource.password=${hera.datasource.password}
mq.nameseraddr=${hera.rocketmq.nameserver}
spring.redis.cluster.nodes=${hera.redis.url}
spring.redis.password=${hera.redis.password}
tpc.token.parse.url=http://mi-tpclogin:8098/login/token/parse
prometheus.token=adqSWsad19E8D4
prometheus.pull.header=application/openmetrics-text; version=1.0.0; charset=utf-8

#ES batch config
es.bulk_actions=1000
es.byte_size=10
es.concurrent_request=20
es.flush_interval=100
es.retry_num=0
es.retry_interval=0

# kafka vpc-9003\9004
# kafka.client.truststore.jks file location
java.security.auth.login.config=
# mix.4096.client.truststore.jks file location
ssl.truststore.location=
sasl.mechanism=
kafka.username=
kafka.password=

# kafka vpc-9004
# kafka_client_jaas_plain.conf file location
java.security.auth.login.config.plain=
# kafka_client_jaas_scram.conf file location
java.security.auth.login.config.scram=

# kafka.vpc.type
# vpc-9002
# vpc-ssl-9003
# vpc-9004
kafka.vpc.type=

kafka.poll.records=500

query.exclude.sql=false
query.exclude.server.ip=false
query.exclude.dubbo.method=false