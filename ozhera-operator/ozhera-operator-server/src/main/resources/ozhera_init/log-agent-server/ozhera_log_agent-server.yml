# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
apiVersion: apps/v1
kind: Deployment
metadata:
  name: log-agent-server
  namespace: ozhera-namespace
  labels:
    app: log-agent-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: log-agent-server
  template:
    metadata:
      labels:
        app: log-agent-server
    spec:
      containers:
        - name: log-agent-server-container
          image: herahub/opensource-pub:log-agent-server-2.2.6-SNAPSHOT-beta-v1
          resources:
            limits:
              cpu: '500m'
              memory: 2Gi
---
apiVersion: v1
kind: Service
metadata:
  name: log-agent-server
  namespace: ozhera-namespace
  labels:
    app: log-agent-server
spec:
  ports:
    - port: 9899
      targetPort: 9899
      protocol: TCP
  selector:
    app: log-agent-server
  clusterIP: None
