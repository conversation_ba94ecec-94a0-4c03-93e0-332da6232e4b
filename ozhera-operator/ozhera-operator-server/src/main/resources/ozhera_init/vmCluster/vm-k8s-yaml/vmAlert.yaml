# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
apiVersion: apps/v1
kind: Deployment
metadata:
  name: victoria-metrics-alert-server
  namespace: ozhera-namespace
  labels:
    app: ozhera-vmalert
    app.kubernetes.io/instance: victoria-metrics-alert
spec:
  replicas: 2
  selector:
    matchLabels:
      app: ozhera-vmalert
      app.kubernetes.io/instance: victoria-metrics-alert
  template:
    metadata:
      labels:
        app: ozhera-vmalert
        app.kubernetes.io/instance: victoria-metrics-alert
    spec:
      volumes:
        - name: alerts-config
          configMap:
            name: ozhera-vmalert-rule
            defaultMode: 420
      containers:
        - name: victoria-metrics-alert-server
          image: victoriametrics/vmalert:v1.96.0
          args:
            - '-rule=http://prometheus-agent-independent.ozhera-namespace/api/v1/vm/reloadAlertRule'
            - >-
              -datasource.url=http://victoria-metrics-victoria-metrics-cluster-vmselect.ozhera-namespace:8481/select/0/prometheus/
            - '-notifier.url=http://ozhera-alertmanager-cluster-0.ozhera-alertmanager-cluster.ozhera-namespace:9093'
            - '-notifier.url=http://ozhera-alertmanager-cluster-1.ozhera-alertmanager-cluster.ozhera-namespace:9093'
            - '-remoteRead.url=http://victoria-metrics-victoria-metrics-cluster-vmselect.ozhera-namespace:8481/select/0/prometheus/'
            - '-remoteWrite.url=http://victoria-metrics-victoria-metrics-cluster-vminsert.ozhera-namespace:8480/insert/0/prometheus/'
            - '-envflag.enable=true'
            - '-envflag.prefix=VM_'
            - '-loggerFormat=json'
            - '-evaluationInterval=30s'
          ports:
            - name: http
              containerPort: 8880
              protocol: TCP
          resources: {}
          volumeMounts:
            - name: alerts-config
              mountPath: /config
          livenessProbe:
            tcpSocket:
              port: http
            initialDelaySeconds: 5
            timeoutSeconds: 5
            periodSeconds: 15
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /health
              port: http
              scheme: HTTP
            initialDelaySeconds: 5
            timeoutSeconds: 5
            periodSeconds: 15
            successThreshold: 1
            failureThreshold: 3
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
          securityContext: {}
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      serviceAccountName: prometheus
      automountServiceAccountToken: true
      securityContext: {}
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600

---
apiVersion: v1
kind: Service
metadata:
  name: victoria-metrics-alert-server
  namespace: ozhera-namespace
  labels:
    app: ozhera-vmalert
    app.kubernetes.io/instance: victoria-metrics-alert
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8880
      targetPort: http
  selector:
    app: ozhera-vmalert
    app.kubernetes.io/instance: victoria-metrics-alert
  type: ClusterIP
  sessionAffinity: None
  ipFamilies:
    - IPv4
  ipFamilyPolicy: SingleStack
  internalTrafficPolicy: Cluster
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ozhera-vmalert-rule
  namespace: ozhera-namespace
data:
  alert.rules: |
    groups:
    - name: example
      rules:
      - alert: HighRequestLatency
        expr: job:request_latency_seconds:mean5m{job="myjob"} > 0.5
        for: 10m
        labels:
          severity: page
        annotations:
          summary: High request latency