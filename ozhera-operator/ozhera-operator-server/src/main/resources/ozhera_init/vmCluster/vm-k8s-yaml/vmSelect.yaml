# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
apiVersion: apps/v1
kind: Deployment
metadata:
  name: victoria-metrics-victoria-metrics-cluster-vmselect
  namespace: ozhera-namespace
  labels:
    app: ozhera-vmselect
    app.kubernetes.io/instance: victoria-metrics
spec:
  replicas: 2
  selector:
    matchLabels:
      app: ozhera-vmselect
      app.kubernetes.io/instance: victoria-metrics
  template:
    metadata:
      labels:
        app: ozhera-vmselect
        app.kubernetes.io/instance: victoria-metrics
    spec:
      volumes:
        - name: cache-volume
          emptyDir: {}
      containers:
        - name: victoria-metrics-cluster-vmselect
          image: victoriametrics/vmselect:v1.96.0-cluster
          args:
            - '--cacheDataPath=/cache'
            - >-
              --storageNode=victoria-metrics-victoria-metrics-cluster-vmstorage-0.victoria-metrics-victoria-metrics-cluster-vmstorage.ozhera-namespace.svc.cluster.local:8401
            - >-
              --storageNode=victoria-metrics-victoria-metrics-cluster-vmstorage-1.victoria-metrics-victoria-metrics-cluster-vmstorage.ozhera-namespace.svc.cluster.local:8401
            - '--envflag.enable=true'
            - '--envflag.prefix=VM_'
            - '--loggerFormat=json'
            - '-dedup.minScrapeInterval=30s'
          ports:
            - name: http
              containerPort: 8481
              protocol: TCP
          volumeMounts:
            - name: cache-volume
              mountPath: /cache
          livenessProbe:
            tcpSocket:
              port: http
            initialDelaySeconds: 5
            timeoutSeconds: 5
            periodSeconds: 15
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /health
              port: http
              scheme: HTTP
            initialDelaySeconds: 5
            timeoutSeconds: 5
            periodSeconds: 15
            successThreshold: 1
            failureThreshold: 3
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
          securityContext: {}
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      serviceAccountName: prometheus
      automountServiceAccountToken: true
      securityContext: {}
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  progressDeadlineSeconds: 600

---
apiVersion: v1
kind: Service
metadata:
  name: victoria-metrics-victoria-metrics-cluster-vmselect
  namespace: ozhera-namespace
  labels:
    app: ozhera-vmselect-svc
    app.kubernetes.io/instance: victoria-metrics
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8481
      targetPort: http
  selector:
    app: ozhera-vmselect
    app.kubernetes.io/instance: victoria-metrics
  type: ClusterIP
  sessionAffinity: None
  ipFamilies:
    - IPv4
  ipFamilyPolicy: SingleStack
  internalTrafficPolicy: Cluster