# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mimonitor
  namespace: ozhera-namespace
  labels:
    app: mimonitor
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mimonitor
  template:
    metadata:
      labels:
        app: mimonitor
    spec:
      containers:
      - name: mimonitor-container
        image: herahub/opensource-pub:ozhera-monitor-server-2.2.6-SNAPSHOT-beta-v1
        resources:
          limits:
            cpu: '500m'
            memory: 2Gi
        imagePullPolicy: Always
        livenessProbe:
          tcpSocket:
            port: 8099
          initialDelaySeconds: 60
---
apiVersion: v1
kind: Service
metadata:
  name: mimonitor
  namespace: ozhera-namespace
  labels:
    app: mimonitor
spec:
  ports:
    - port: 8099
      targetPort: 8099
      protocol: TCP
  selector:
    app: mimonitor
  clusterIP: None
