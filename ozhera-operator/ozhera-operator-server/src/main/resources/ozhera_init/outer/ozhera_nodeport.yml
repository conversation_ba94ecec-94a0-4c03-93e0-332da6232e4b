# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
apiVersion: v1
kind: Service
metadata:
  name: hera-nginx
  namespace: ozhera-namespace
spec:
  ports:
    - port: 80
      targetPort: 7001
      protocol: TCP
      nodePort: 31000
  selector:
    app: hera-nginx-fe
  type: NodePort

---
apiVersion: v1
kind: Service
metadata:
  name: tpc-nginx
  namespace: ozhera-namespace
spec:
  ports:
    - port: 80
      targetPort: 7001
      protocol: TCP
      nodePort: 31001
  selector:
    app: tpc-nginx-fe
  type: NodePort

---
apiVersion: v1
kind: Service
metadata:
  name: tpclogin-nginx
  namespace: ozhera-namespace
spec:
  ports:
    - port: 80
      targetPort: 7001
      protocol: TCP
      nodePort: 31002
  selector:
    app: tpclogin-nginx-fe
  type: NodePort

---
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: ozhera-namespace
spec:
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
      nodePort: 31003
  selector:
    app: grafana
  type: NodePort

---
apiVersion: v1
kind: Service
metadata:
  name: alertmanager
  namespace: ozhera-namespace
  labels:
    k8s-app: alertmanager
spec:
  type: NodePort
  ports:
    - name: http
      port: 9093
      targetPort: 9093
      nodePort: 31004
  selector:
    k8s-app: alertmanager

---
apiVersion: v1
kind: Service
metadata:
  name: prometheus
  namespace: ozhera-namespace
spec:
  ports:
    - port: 9090
      targetPort: 9090
      protocol: TCP
      nodePort: 31006
  selector:
    app: prometheus
  type: NodePort