# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# For prometheus service use on the cloud
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    name: prometheus-agent-deployment
  name: prometheus-agent-independent
  namespace: ozhera-namespace
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus-agent-independent
  template:
    metadata:
      labels:
        app: prometheus-agent-independent
    spec:
      serviceAccountName: prometheus
      imagePullSecrets:
        - name: mione-opensource-credential
      containers:
        - name: prometheus-agent-independent
          imagePullPolicy: Always
          image: herahub/opensource-pub:ozhera-prometheus-agent-server-2.2.6-SNAPSHOT-beta-v1
          ports:
            - containerPort: 8080
              protocol: TCP
---
apiVersion: v1
kind: Service
metadata:
  name: prometheus-agent-independent
  namespace: ozhera-namespace
spec:
  ports:
    - port: 80
      targetPort: 8080
      protocol: TCP
  selector:
    app: prometheus-agent-independent
  type: LoadBalancer