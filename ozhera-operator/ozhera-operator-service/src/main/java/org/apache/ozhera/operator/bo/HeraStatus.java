/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.ozhera.operator.bo;


import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class HeraStatus {

    public static final int STATUS_FAILED = -1;
    public static final int STATUS_DEFAULT = 0;
    public static final int STATUS_SUCCESS = 1;

    /**
     * -1：success
     *  0：default
     *  1：fail
     */
    private int status;

    private String msg;
}
