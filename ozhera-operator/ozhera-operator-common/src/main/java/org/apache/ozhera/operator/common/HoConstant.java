/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.ozhera.operator.common;

/**
 * <AUTHOR>
 * @date 2022-06-24
 */
public class HoConstant {

    public static final String HERA_NAMESPACE = "ozhera-namespace";

    public static final String HERA_CR_NAME = "ozhera-bootstrap";

    public static final String DEPLOYMENT_NAME_REDIS = "";

    public static final String DEPLOYMENT_NAME_NACOS = "";

    public static final String DEPLOYMENT_NAME_MYSQL = "";

    public static final String DEPLOYMENT_NAME_RMQ = "";

    public static final String DEPLOYMENT_NAME_MIFAAS_DASH = "";

    public static final String DEPLOYMENT_NAME_TPC = "";

    public static final String KEY_NACOS_ADDRESS = "hera.nacos.address";

    public static final String KEY_NACOS_USERNAME = "hera.nacos.username";

    public static final String KEY_NACOS_PASSWORD = "hera.nacos.password";

    public static final String KEY_ROCKETMQ_NAMESERVER = "hera.rocketmq.nameserver";

    public static final String KEY_ROCKETMQ_AK = "hera.rocketmq.ak";

    public static final String KEY_ROCKETMQ_SK = "hera.rocketmq.sk";

    public static final String KEY_ES_URL = "hera.es.url";

    public static final String KEY_ES_USERNAME = "hera.es.username";

    public static final String KEY_ES_PASSWORD = "hera.es.password";

    public static final String KEY_REDIS_URL = "hera.redis.url";

    public static final String KEY_REDIS_PASSWORD = "hera.redis.password";

    public static final String KEY_DATASOURCE_URL = "hera.datasource.url";

    public static final String KEY_DATASOURCE_USERNAME = "hera.datasource.username";

    public static final String KEY_DATASOURCE_PASSWORD = "hera.datasource.password";

    public static final String KEY_TPC_LOGIN_FE_URL = "tpc.login.fe.url";

    public static final String KEY_GRAFANA_URL = "hera.grafana.url";

    public static final String KEY_PROMETHEUS_URL = "hera.prometheus.url";

    public static final String KEY_ALERTMANAGER_URL = "hera.alertmanager.url";

    public static final String KEY_HERA_URL = "hera.homepage.url";

    public static final String KEY_HERA_TPC_URL = "hera.tpc.url";


}
