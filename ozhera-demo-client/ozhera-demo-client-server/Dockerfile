# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

FROM openjdk:21-jdk-bookworm

RUN echo 'Asia/Shanghai' >/etc/timezone
ARG SRC_PATH
ARG APP_VERSION
COPY ${SRC_PATH}/ozhera-demo-client-server-${APP_VERSION}.jar /home/<USER>/ozhera-demo-client-server.jar
COPY ./opentelemetry-javaagent.jar /opt/soft/opentelemetry-javaagent.jar

#ENTRYPOINT ["sh","-c","java -javaagent:/opt/soft/opentelemetry-javaagent.jar -Dotel.exporter.prometheus.nacos.addr=nacos:80 -Dotel.exporter.log.pathprefix=/home/<USER>/log/ -XX:+UseZGC --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.math=ALL-UNNAMED --add-opens java.base/sun.reflect=ALL-UNNAMED --add-opens java.base/java.xml=ALL-UNNAMED --add-exports java.base/sun.reflect.annotation=ALL-UNNAMED --add-opens jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED --add-exports java.xml/com.sun.org.apache.xerces.internal.impl.dv.util=ALL-UNNAMED -Duser.timezone=Asia/Shanghai -jar /home/<USER>/ozhera-demo-client-server.jar"]

COPY ${SRC_PATH}/lib /home/<USER>/lib/

ENTRYPOINT ["java", "-javaagent:/opt/soft/opentelemetry-javaagent.jar", "-Dotel.exporter.prometheus.nacos.addr=nacos:80", "-Dotel.exporter.log.pathprefix=/home/<USER>/log/", "-XX:+UseZGC", "--add-opens=java.base/java.lang=ALL-UNNAMED", "--add-opens=java.base/java.util=ALL-UNNAMED", "--add-opens=java.base/java.math=ALL-UNNAMED", "--add-opens=java.base/sun.reflect=ALL-UNNAMED", "--add-opens=java.base/java.xml=ALL-UNNAMED", "--add-exports=java.base/sun.reflect.annotation=ALL-UNNAMED", "--add-opens=jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED", "--add-exports=java.xml/com.sun.org.apache.xerces.internal.impl.dv.util=ALL-UNNAMED", "-Duser.timezone=Asia/Shanghai", "-cp", "/home/<USER>/ozhera-demo-client-server.jar:/home/<USER>/lib/*", "org.apache.ozhera.demo.client.bootstrap.HeraDemoClientBootstrap"]