<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->

# Apache OzHera(Incubating)

<p align="center">
<b>Apache OzHera(Incubating):云原生时代的应用观测平台</b>
</p>

<p align="center">
<a href="README_CN.md"><img src="./readme/images/doc_logo_cn.svg" alt="CN doc"></a>
<a href="README.md"><img src="./readme/images/doc_logo_english.svg" alt="EN doc"></a>
</p>


## Apache OzHera(Incubating)是什么？
Apache OzHera(Incubating)是一款云原生时代的应用观测平台(APM)。以应用为核心，集指标监控、链路追踪、日志、报警于一身，并实现了metrics->tracing->logging的串联和联动，Apache OzHera(Incubating)还提供应用健康状态列表、应用指标看板、接口大盘、应用大盘、网关大盘等内容丰富的监测看板，以及简洁明了的可视化明文报警，让用户准确、高效定位故障。

---

## Architecture
![ozhera](readme/images/architecture.png)

---

## Features
- 准：基于业务错误码提取可用性指标
- 快：metrics-tracing-logging联动
- 经济：<0.1%存储成本，满足99.9%的tracing诉求
- 拥抱云原生：遵循Opentracing标准、深度适配K8S、集成Opentelemetry、Grafana、Prometheus、ES等多个开源明星产品
- 企业级可观测产品

---

## Getting Started
### 在线体验
+ [在线体验地址](https://ozhera.demo.m.one.mi.com/)
+ username: <EMAIL>
+ password: 123456

---

### 官方网站

欢迎访问Apache OzHera(Incubating)的[官方网站](https://ozhera.apache.org/)

---

### 部署
[operator使用文档.md](readme/deploy/ozhera-deploy-document_cn.md)

---

### 应用接入
[应用接入文档.md](readme/application-integeration/application-integration-document_cn.md)

---

### 用户手册
[用户手册.md](readme%2Fuser-manual%2Fuser-manual-document_cn.md)

---

### 贡献
欢迎贡献者加入ozhera项目。请查看 [contributing_cn.md](CONTRIBUTING_CN.md) 以了解如何为该项目做出贡献.

我该如何贡献？
- 请查看带有标签 [good first issue](https://github.com/apache/ozhera/labels/good%20first%20issue) 或 [contribution welcome](https://github.com/apache/ozhera/labels/help%20wanted)的问题.
- 回答 [issues](https://github.com/apache/ozhera/issues)上的问题.
- 修复 [issues](https://github.com/apache/ozhera/issues)上报告的错误,并向我们发送一个拉取请求.
- 审查现有的 [pull request](https://github.com/apache/ozhera/pulls).

---

### 联系我们

我们非常欢迎您对项目提出宝贵的意见和建议。无论您有技术问题、功能需求还是想要加入我们的开发团队，都可以通过以下方式与我们取得联系：

#### 邮件联系
- **邮箱地址**: [<EMAIL>](mailto:<EMAIL>)
- **响应时间**: 我们会在收到邮件后的24小时内回复

#### 社区交流
- 我们也欢迎您加入我们的社区，参与讨论和交流：
    - 欢迎访问Apache OzHera(Incubating)的[社区网站](https://ozhera.apache.org/ )， 我们会将Apache OzHera(Incubating)的技术向分享、思考与最新成果发布在上面， 欢迎大家共同交流
    - [GitHub Discussions](https://github.com/apache/ozhera/issues?q=is%3Aissue+is%3Aopen+%5BDisscusion%5D+)

---


