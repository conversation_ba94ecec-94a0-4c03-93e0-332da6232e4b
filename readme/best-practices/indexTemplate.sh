#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

curl --location --request PUT 'http://elasticsearch:9200/_template/mione-staging-zgq-driver' \
--header 'Content-type: application/json; charset=UTF-8' \
--data '{"index_patterns":["mione-staging-zgq-driver*"],"settings":{"index":{"lifecycle":{"name":"7Del"},"routing":{"allocation":{"total_shards_per_node":"2"}},"number_of_shards":"1","number_of_replicas":"0"}},"mappings":{"_meta":{},"_source":{},"properties":{"timeStamp":{"type":"keyword"},"password":{"type":"keyword"},"appName":{"type":"keyword"},"ip":{"type":"keyword"},"dataBaseName":{"type":"keyword"},"domainPort":{"type":"keyword"},"type":{"type":"keyword"},"userName":{"type":"keyword"},"timestamp":{"type":"date"}}},"aliases":{"mione-staging-zgq-driver":{}}}'

curl --location --request PUT 'http://elasticsearch:9200/_template/mione-staging-zgq-jaeger-span' \
--header 'Content-type: application/json; charset=UTF-8' \
--data '{"index_patterns":["mione-staging-zgq-jaeger-span*"],"settings":{"index":{"lifecycle":{"name":"7Del","rollover_alias":"mione-staging-zgq-jaeger-span"},"routing":{"allocation":{"total_shards_per_node":"6"}},"number_of_shards":"1","number_of_replicas":"0"}},"mappings":{"_meta":{},"_source":{},"properties":{"traceID":{"ignore_above":256,"type":"keyword"},"process":{"type":"object","properties":{"tag":{"type":"object"},"serviceName":{"ignore_above":256,"type":"keyword"},"tags":{"type":"nested","properties":{"tagType":{"ignore_above":256,"type":"keyword"},"type":{"type":"keyword"},"value":{"ignore_above":256,"type":"keyword"},"key":{"ignore_above":256,"type":"keyword"}}}}},"references":{"type":"nested","properties":{"spanID":{"ignore_above":256,"type":"keyword"},"traceID":{"ignore_above":256,"type":"keyword"},"refType":{"ignore_above":256,"type":"keyword"}}},"startTimeMillis":{"format":"epoch_millis","type":"date"},"flags":{"type":"integer"},"operationName":{"ignore_above":256,"type":"keyword"},"parentSpanID":{"ignore_above":256,"type":"keyword"},"tags":{"type":"nested","properties":{"val\r\nue":{"type":"keyword"},"v\r\nalue":{"type":"keyword"},"tagType":{"ignore_above":256,"type":"keyword"},"\r\nvalue":{"type":"keyword"},"type":{"type":"keyword"},"value":{"ignore_above":256,"type":"keyword"},"key":{"ignore_above":256,"type":"keyword"},"key\r\n":{"type":"keyword"}}},"duration":{"type":"long"},"spanID":{"ignore_above":256,"type":"keyword"},"@timestamp":{"format":"epoch_millis||strict_date_optional_time","type":"date"},"startTime":{"type":"long"},"tag":{"type":"object"},"logs":{"type":"nested","properties":{"fields":{"type":"nested","properties":{"v\r\nalue":{"type":"keyword"},"tagType":{"ignore_above":256,"type":"keyword"},"type":{"type":"keyword"},"value":{"ignore_above":256,"type":"keyword"},"key":{"ignore_above":256,"type":"keyword"}}},"timestamp":{"type":"long"}}},"timestamp":{"type":"date"}}},"aliases":{"mione-staging-zgq-jaeger-span":{}}}'

curl --location --request PUT 'http://elasticsearch:9200/_template/mione_hera_log_single_app_log01' \
--header 'Content-type: application/json; charset=UTF-8' \
--data '{"index_patterns":["mione_hera_log_single_app_log01*"],"settings":{"index":{"lifecycle":{"name":"7Del"},"routing":{"allocation":{"total_shards_per_node":"2"}},"number_of_shards":"1","number_of_replicas":"0"}},"mappings":{"dynamic":"true","dynamic_templates":[{"strings_as_keywords":{"match_mapping_type":"string","mapping":{"type":"keyword"}}}],"properties":{"app":{"type":"keyword","ignore_above":4096},"appName":{"type":"keyword","ignore_above":4096},"className":{"type":"keyword"},"code":{"type":"keyword","ignore_above":4096},"costTime":{"type":"keyword","ignore_above":4096},"errorInfo":{"type":"text"},"filename":{"type":"keyword"},"group":{"type":"text"},"ip":{"type":"keyword","ignore_above":4096},"level":{"type":"keyword","ignore_above":4096},"line":{"type":"text"},"linenumber":{"type":"long"},"logip":{"type":"keyword","ignore_above":4096},"logsource":{"type":"text"},"logstore":{"type":"keyword","ignore_above":4096},"machine":{"type":"keyword"},"message":{"type":"text"},"methodName":{"type":"keyword"},"mqtag":{"type":"keyword","ignore_above":4096},"mqtopic":{"type":"keyword","ignore_above":4096},"noKnow":{"type":"keyword","ignore_above":4096},"other":{"type":"text"},"packageName":{"type":"keyword","ignore_above":4096},"params":{"type":"text"},"pid":{"type":"keyword","ignore_above":4096},"podName":{"type":"keyword"},"result":{"type":"text"},"search":{"type":"text"},"server":{"type":"keyword"},"tail":{"type":"keyword","ignore_above":4096},"thread":{"type":"text"},"threadName":{"type":"keyword"},"timestamp":{"type":"date","format":"epoch_millis"},"traceId":{"type":"keyword","ignore_above":4096}}},"aliases":{"mione_hera_log_single_app_log01":{}}}'

curl --location --request PUT 'http://elasticsearch:9200/_template/mione-staging-zgq-jaeger-service' \
--header 'Content-type: application/json; charset=UTF-8' \
--data '{"index_patterns":["mione-staging-zgq-jaeger-service*"],"settings":{"index":{"lifecycle":{"name":"7Del"},"routing":{"allocation":{"total_shards_per_node":"2"}},"number_of_shards":"1","number_of_replicas":"0"}},"mappings":{"_meta":{},"_source":{},"properties":{"operationName":{"type":"keyword"},"dataJson":{"type":"keyword"},"serviceName":{"type":"keyword"},"timestamp":{"type":"date"}}},"aliases":{"mione-staging-zgq-jaeger-service":{}}}'

curl --location --request PUT 'http://elasticsearch:9200/_template/mione_hera_log_other_app_log01' \
--header 'Content-type: application/json; charset=UTF-8' \
--data '{"index_patterns":["mione_hera_log_other_app_log01*"],"settings":{"index":{"lifecycle":{"name":"7Del"},"routing":{"allocation":{"total_shards_per_node":"2"}},"number_of_shards":"1","number_of_replicas":"0"}},"mappings":{"dynamic":"true","dynamic_templates":[{"strings_as_keywords":{"match_mapping_type":"string","mapping":{"type":"keyword"}}}],"properties":{"app":{"type":"keyword","ignore_above":4096},"appName":{"type":"keyword","ignore_above":4096},"className":{"type":"keyword"},"code":{"type":"keyword","ignore_above":4096},"costTime":{"type":"keyword","ignore_above":4096},"errorInfo":{"type":"text"},"filename":{"type":"keyword"},"group":{"type":"text"},"ip":{"type":"keyword","ignore_above":4096},"level":{"type":"keyword","ignore_above":4096},"line":{"type":"text"},"linenumber":{"type":"long"},"logip":{"type":"keyword","ignore_above":4096},"logsource":{"type":"text"},"logstore":{"type":"keyword","ignore_above":4096},"machine":{"type":"keyword"},"message":{"type":"text"},"methodName":{"type":"keyword"},"mqtag":{"type":"keyword","ignore_above":4096},"mqtopic":{"type":"keyword","ignore_above":4096},"noKnow":{"type":"keyword","ignore_above":4096},"other":{"type":"text"},"packageName":{"type":"keyword","ignore_above":4096},"params":{"type":"text"},"pid":{"type":"keyword","ignore_above":4096},"podName":{"type":"keyword"},"result":{"type":"text"},"search":{"type":"text"},"server":{"type":"keyword"},"tail":{"type":"keyword","ignore_above":4096},"thread":{"type":"text"},"threadName":{"type":"keyword"},"timestamp":{"type":"date","format":"epoch_millis"},"traceId":{"type":"keyword","ignore_above":4096}}},"aliases":{"mione_hera_log_other_app_log01":{}}}'

curl --location --request PUT 'http://elasticsearch:9200/_template/mione_hera_log_multiple_app_log01' \
--header 'Content-type: application/json; charset=UTF-8' \
--data '{"index_patterns":["mione_hera_log_multiple_app_log01*"],"settings":{"index":{"lifecycle":{"name":"7Del"},"routing":{"allocation":{"total_shards_per_node":"2"}},"number_of_shards":"1","number_of_replicas":"0"}},"mappings":{"dynamic":"true","dynamic_templates":[{"strings_as_keywords":{"match_mapping_type":"string","mapping":{"type":"keyword"}}}],"properties":{"app":{"type":"keyword","ignore_above":4096},"appName":{"type":"keyword","ignore_above":4096},"className":{"type":"keyword"},"code":{"type":"keyword","ignore_above":4096},"costTime":{"type":"keyword","ignore_above":4096},"errorInfo":{"type":"text"},"filename":{"type":"keyword"},"group":{"type":"text"},"ip":{"type":"keyword","ignore_above":4096},"level":{"type":"keyword","ignore_above":4096},"line":{"type":"text"},"linenumber":{"type":"long"},"logip":{"type":"keyword","ignore_above":4096},"logsource":{"type":"text"},"logstore":{"type":"keyword","ignore_above":4096},"machine":{"type":"keyword"},"message":{"type":"text"},"methodName":{"type":"keyword"},"mqtag":{"type":"keyword","ignore_above":4096},"mqtopic":{"type":"keyword","ignore_above":4096},"noKnow":{"type":"keyword","ignore_above":4096},"other":{"type":"text"},"packageName":{"type":"keyword","ignore_above":4096},"params":{"type":"text"},"pid":{"type":"keyword","ignore_above":4096},"podName":{"type":"keyword"},"result":{"type":"text"},"search":{"type":"text"},"server":{"type":"keyword"},"tail":{"type":"keyword","ignore_above":4096},"thread":{"type":"text"},"threadName":{"type":"keyword"},"timestamp":{"type":"date","format":"epoch_millis"},"traceId":{"type":"keyword","ignore_above":4096}}},"aliases":{"mione_hera_log_multiple_app_log01":{}}}'

curl --location --request PUT 'http://elasticsearch:9200/_template/mione_hera_log_nginx_app_log01' \
--header 'Content-type: application/json; charset=UTF-8' \
--data '{"index_patterns":["mione_hera_log_nginx_app_log01*"],"settings":{"index":{"lifecycle":{"name":"7Del"},"routing":{"allocation":{"total_shards_per_node":"2"}},"number_of_shards":"1","number_of_replicas":"0"}},"mappings":{"dynamic":"true","dynamic_templates":[{"strings_as_keywords":{"match_mapping_type":"string","mapping":{"type":"keyword"}}}],"properties":{"app":{"type":"keyword","ignore_above":4096},"appName":{"type":"keyword","ignore_above":4096},"className":{"type":"keyword"},"code":{"type":"keyword","ignore_above":4096},"costTime":{"type":"keyword","ignore_above":4096},"errorInfo":{"type":"text"},"filename":{"type":"keyword"},"group":{"type":"text"},"ip":{"type":"keyword","ignore_above":4096},"level":{"type":"keyword","ignore_above":4096},"line":{"type":"text"},"linenumber":{"type":"long"},"logip":{"type":"keyword","ignore_above":4096},"logsource":{"type":"text"},"logstore":{"type":"keyword","ignore_above":4096},"machine":{"type":"keyword"},"message":{"type":"text"},"methodName":{"type":"keyword"},"mqtag":{"type":"keyword","ignore_above":4096},"mqtopic":{"type":"keyword","ignore_above":4096},"noKnow":{"type":"keyword","ignore_above":4096},"other":{"type":"text"},"packageName":{"type":"keyword","ignore_above":4096},"params":{"type":"text"},"pid":{"type":"keyword","ignore_above":4096},"podName":{"type":"keyword"},"result":{"type":"text"},"search":{"type":"text"},"server":{"type":"keyword"},"tail":{"type":"keyword","ignore_above":4096},"thread":{"type":"text"},"threadName":{"type":"keyword"},"timestamp":{"type":"date","format":"epoch_millis"},"traceId":{"type":"keyword","ignore_above":4096}}},"aliases":{"mione_hera_log_nginx_app_log01":{}}}'

curl --location --request PUT 'http://elasticsearch:9200/_template/zgq_common_staging_private_prometheus' \
--header 'Content-type: application/json; charset=UTF-8' \
--data '{"index_patterns":["zgq_common_staging_private_prometheus*"],"settings":{"index":{"lifecycle":{"name":"1Warm_7Del"},"routing":{"allocation":{"total_shards_per_node":"2"}},"number_of_shards":"1","number_of_replicas":"0"}},"mappings":{"_meta":{},"_source":{},"properties":{"traceId":{"ignore_above":4096,"type":"keyword"},"functionName":{"ignore_above":4096,"type":"keyword"},"errorType":{"ignore_above":4096,"type":"keyword"},"moduleName":{"ignore_above":4096,"type":"keyword"},"errorCode":{"ignore_above":4096,"type":"keyword"},"env":{"ignore_above":4096,"type":"keyword"},"serviceName":{"ignore_above":4096,"type":"keyword"},"type":{"ignore_above":4096,"type":"keyword"},"url":{"ignore_above":4096,"type":"keyword"},"duration":{"ignore_above":4096,"type":"keyword"},"serverEnv":{"ignore_above":4096,"type":"keyword"},"functionId":{"ignore_above":4096,"type":"keyword"},"domain":{"ignore_above":4096,"type":"keyword"},"host":{"type":"ip"},"_class":{"ignore_above":4096,"type":"keyword"},"dataSource":{"ignore_above":4096,"type":"keyword"},"group":{"ignore_above":4096,"type":"keyword"},"timestamp":{"format":"epoch_millis","type":"date"}}},"aliases":{"zgq_common_staging_private_prometheus":{}}}'
