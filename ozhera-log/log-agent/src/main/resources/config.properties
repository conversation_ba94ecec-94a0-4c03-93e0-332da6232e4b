# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
app_name=${app_name}
app_max_index=30

nacosAddr=${nacosAddr}
serviceName=${serviceName}

log.path=${log.path}
# agent采集进度存储路径
agent.memory.path=${agent.memory.path}
# rpc:从远程log-manager读取 || json: 从本地agent_channel_config.json读取
agent.channel.locator=${agent.channel.locator}

logFile=com.xiaomi.mone.file.LogFile

kafka.use.ssl=true
kafka.sll.location=/opt/app/mix.4096.client.truststore.jks

app_id=${app_id}
env_id=${env_id}
env_name=${env_name}

registration_initiation_flag=true

filter_log_level_prefix_length = ${filter_log_level_prefix_length}