# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
app_name=log_agent_dev
nacosAddr=127.0.0.1:80
serviceName=milog_manager_server

log.path=/home/<USER>/log/log-agent
agent.memory.path=/home/<USER>/log/log-agent
agent.channel.locator=json

app_id=10010
env_id=1
env_name=default_env

filter_log_level_prefix_length = 60