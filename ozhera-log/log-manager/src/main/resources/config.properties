# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
nacosAddr=${nacosAddr}
defaultNacosAddres=${defaultNacosAddres}
nacos_config_dataid=${nacos_config_dataid}
nacos_config_group=DEFAULT_GROUP
nacos_config_server_addr=${defaultNacosAddres}
close_nacos_plugin=false

serverName=${serverName}
serverNameHttp=${serverNameHttp}
serverPort=${serverPort}

db_pool_size=${db_pool_size}
app.env=${app.env}
driver.class=org.mariadb.jdbc.Driver

# 允许跨域
allow-cross-domain=true
# http响应返回原生对象(没有经过包装)
response-original-value=true
log.path=${log.path}
# rocketmq conf
rocketmq_consumer_on=${rocketmq_consumer_on}

rocketmq_consumer_group=${rocketmq_consumer_group}
rocketmq_consumer_topic=${rocketmq_consumer_topic}
rocketmq_consumer_tag=${rocketmq_consumer_tag}
rocketmq_consumer_scale_tag=${rocketmq_consumer_scale_tag}
miline_rocketmq_consumer_topic=${miline_rocketmq_consumer_topic}
miline_rocketmq_consumer_tag=${miline_rocketmq_consumer_tag}
miline_rocketmq_consumer_group=${miline_rocketmq_consumer_group}

#dubbo
dubbo_app_name=${dubbo_app_name}
dubbo.group=${dubbo.group}
dubbo.env.group=${dubbo.env.group}
dubbo_reg_check=${dubbo_reg_check}
dubbo_reg_address=${dubbo_reg_address}
nacos.config.addrs=${nacos.config.addrs}
db_open_transactional=true
db_print_sql=false

milogpattern=timestamp,level,traceId,threadName,className,line,message
mybatis_mapper_location=mapper/MilogEsClusterMapper.xml,mapper/MilogEsIndexMapper.xml,mapper/MilogLogProcessMapper.xml,mapper/MilogLogTemplateDetailMapper.xml,mapper/MilogLogTemplateMapper.xml,mapper/MilogLogCountMapper.xml,mapper/MilogLogstailMapper.xml,mapper/MilogLogSearchSaveMapper.xml,mapper/MilogEsClusterMapper.xml,mapper/MilogLogNumAlertMapper.xml,mapper/MilogAnalyseDashboardMapper.xml,mapper/MilogAnalyseGraphMapper.xml,mapper/MilogAnalyseDashboardGraphRefMapper.xml,mapper/MilogAnalyseGraphTypeMapper.xml

#只发送mq，不消费的日志类型
log_type_mq_not_consume=${log_type_mq_not_consume}
#本地默认登录用户
default_login_user_emp_id=${default_login_user_emp_id}

europe.ip.key=europe.ip

server.type=${server.type}
job_start_flag=${job_start_flag}
tpc_dubbo_group=${tpc_dubbo_group}
tpc_node_code=${tpc_node_code}

filter_urls=${filter_urls}
agent.heart.senders=${agent.heart.senders}
download_file_path=/tmp
tpc.devMode=${tpc.devMode}

kafka.use.ssl=true
kafka.sll.location=/opt/app/mix.4096.client.truststore.jks

