<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.ozhera.log.manager.mapper.MilogLogCountMapper">

    <insert id="batchInsert">
        INSERT INTO milog_log_count
            (tail_id, es_index, day,number)
        VALUES
        <foreach collection="logCountList" item="model" separator=",">
            (#{model.tailId}, #{model.esIndex}, #{model.day}, #{model.number})
        </foreach>
    </insert>
    <delete id="deleteBeforeDay">
        DELETE
        FROM
            milog_log_count
        WHERE
            <![CDATA[day < #{day}]]>
    </delete>
    <delete id="deleteThisDay">
        DELETE
        FROM
            milog_log_count
        WHERE
            <![CDATA[day = #{day}]]>
    </delete>

    <select id="collectTopCount" resultType="java.util.Map" parameterType="string">
        SELECT
            tail.tail,
            sum( count.number ) number
        FROM
            milog_log_count count,
            milog_logstail tail
        WHERE
            count.tail_id = tail.id
            <![CDATA[and count.day between #{fromDay} and #{toDay}]]>
        GROUP BY
            tail.id
        ORDER BY
            number DESC
        LIMIT 10
    </select>

    <select id="collectTrend" resultType="java.util.Map">
        SELECT
            count.day,
            count.number
        FROM
            milog_log_count count,
	        milog_logstail tail
        WHERE
            count.tail_id = tail.id
            and tail.id = #{tailId}
            <![CDATA[and count.day between #{fromDay} and #{toDay}]]>
        ORDER BY
            count.day
    </select>

    <select id="collectSpaceTrend" resultType="java.util.Map">
        SELECT
            count. DAY day,
            sum(count.number) number,
            space.space_name spaceName,
            space.id spaceId
        FROM
            milog_log_count count,
            milog_logstail tail,
            milog_space space
        WHERE
            count.tail_id = tail.id
            AND tail.space_id = space.id
            <![CDATA[and count.day between #{fromDay} and #{toDay}]]>
        GROUP BY
            tail.space_id,
            count.day
        ORDER BY
            spaceId,
            count. DAY
    </select>

    <select id="isLogtailCountDone" resultType="java.lang.Long">
        SELECT
            count( 1 )
        FROM
            `milog_log_count`
        WHERE
            <![CDATA[day = #{day}]]>
    </select>

    <select id="collectSpaceCount" resultType="java.util.Map">
        SELECT
            sum(c.number) number,
            s.id spaceId,
            s.space_name spaceName
        FROM
            milog_logstail t,
            milog_log_count c,
            milog_space s
        WHERE
            t.id = c.tail_id
            AND t.space_id = s.id
            <![CDATA[and c.day between #{fromDay} and #{toDay}]]>
        GROUP BY
            t.space_id
        ORDER BY
            number DESC
        LIMIT 10
    </select>
    <select id="collectAppLog" resultType="java.util.Map">
        SELECT
            sum( count.number ) number,
            tail.milog_app_id milogAppId
        FROM
            milog_log_count count,
            milog_logstail tail
        WHERE
            count.tail_id = tail.id
          AND <![CDATA[count.day = #{day}]]>
        GROUP BY
            tail.milog_app_id
        HAVING
            <![CDATA[number >= #{threshold}]]>
    </select>
</mapper>
