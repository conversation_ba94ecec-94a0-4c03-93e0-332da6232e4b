<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.ozhera.log.manager.mapper.MilogEsClusterMapper">

    <select id="selectByTag" resultType="org.apache.ozhera.log.manager.model.pojo.MilogEsClusterDO">
        SELECT
            *
        FROM
            `milog_es_cluster`
        WHERE
            tag = #{tag}
    </select>
    <select id="selectAll" resultType="org.apache.ozhera.log.manager.model.pojo.MilogEsClusterDO">
        SELECT
            *
        FROM
            `milog_es_cluster`
    </select>
    <select id="selectByRegion" resultType="org.apache.ozhera.log.manager.model.pojo.MilogEsClusterDO">
        SELECT
            *
        FROM
            `milog_es_cluster`
        WHERE
            region = #{region}
    </select>
    <select id="selectByArea" resultType="org.apache.ozhera.log.manager.model.pojo.MilogEsClusterDO">
        SELECT
            *
        FROM
            `milog_es_cluster`
        WHERE
            area = #{area}
            and labels like CONCAT('%',#{label},'%');
    </select>
    <select id="selectByAlias" resultType="org.apache.ozhera.log.manager.model.pojo.MilogEsClusterDO">
        SELECT
            *
        FROM
            `milog_es_cluster`
        WHERE
            name = #{alias}
    </select>
    <select id="selectByTagAndArea" resultType="org.apache.ozhera.log.manager.model.pojo.MilogEsClusterDO">
        SELECT
            *
        FROM
            `milog_es_cluster`
        WHERE
            tag = #{tag}
          and area = #{area};
    </select>
</mapper>
