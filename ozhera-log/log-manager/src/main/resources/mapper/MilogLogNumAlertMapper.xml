<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.ozhera.log.manager.mapper.MilogLogNumAlertMapper">

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO milog_log_num_alert
            (day, app_id, alert_user)
        VALUES
        <foreach collection ="doList" item="do" separator =",">
            (#{do.day}, #{do.appId}, #{do.alertUser})
        </foreach >
    </insert>
    <delete id="deleteThisDay">
        DELETE
        FROM
            milog_log_num_alert
        WHERE
            <![CDATA[day <= #{day}]]>
    </delete>

    <select id="isSend" resultType="java.lang.Long">
        SELECT
            1
        FROM
            milog_log_num_alert
        WHERE
            app_id = #{appId}
        and DAY = #{day}
    </select>
</mapper>
