<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.apache.ozhera</groupId>
        <artifactId>ozhera-log</artifactId>
        <version>2.2.6-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>log-manager</artifactId>
    <version>2.2.7-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>

        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>1.26</version>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>excel</artifactId>
            <version>1.5.0-jdk21</version>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>5.1.48</version>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>nacos</artifactId>
        </dependency>

        <dependency>
            <artifactId>mi-tpclogin-sdk</artifactId>
            <groupId>run.mone</groupId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>dubbo</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.spring</groupId>
                    <artifactId>spring-context-support</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>docean</artifactId>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>docean-plugin-configuration</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.ozhera</groupId>
            <artifactId>app-common</artifactId>
            <version>2.2.6-SNAPSHOT</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>run.mone</groupId>-->
<!--            <artifactId>excel</artifactId>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>log4j</groupId>-->
<!--                    <artifactId>log4j</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>docean-plugin-dubbo</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.dubbo</groupId>
                    <artifactId>dubbo</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>nacos-client</artifactId>
                    <groupId>com.alibaba.nacos</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>dubbo-registry-nacos</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>docean-plugin-db</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>mysql</groupId>
                    <artifactId>mysql-connector-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>docean-plugin-nacos</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.nacos</groupId>
                    <artifactId>nacos-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>es</artifactId>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>docean-plugin-es</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>run.mone</groupId>
                    <artifactId>es</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>docean-plugin-es-antlr4</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>run.mone</groupId>
                    <artifactId>es</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>docean-plugin-mybatis-plus</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>nutz</artifactId>
        </dependency>
        <!-- Internal dependencies -->
        <dependency>
            <groupId>org.apache.ozhera</groupId>
            <artifactId>log-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.ozhera</groupId>
            <artifactId>log-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.ozhera</groupId>
            <artifactId>app-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-acl</artifactId>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>docean-plugin-rocketmq</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>snakeyaml</artifactId>
                    <groupId>org.yaml</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpclient</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>feishu</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <artifactId>mi-tpc-api</artifactId>
            <groupId>run.mone</groupId>
            <exclusions>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>hera-trace</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.ozhera</groupId>
            <artifactId>app-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
        </dependency>

    </dependencies>

    <build>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>${project.basedir}/../../</directory>
                <filtering>true</filtering>
                <includes>
                    <include>DISCLAIMER</include>
                    <include>NOTICE</include>
                    <include>LICENSE</include>
                </includes>
                <targetPath>META-INF/</targetPath>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <compilerVersion>21</compilerVersion>
                    <source>21</source>
                    <target>21</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>apache-release</id>
            <properties>
                <profiles.active>apache-release</profiles.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <filters>
                    <filter>src/main/resources/config/open.properties</filter>
                </filters>
            </build>
        </profile>

    </profiles>

</project>