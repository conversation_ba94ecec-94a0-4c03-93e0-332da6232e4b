# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

app_name=${app_name}
nacos_config_dataid=${nacos_config_dataid}
nacos_config_group=DEFAULT_GROUP
nacos_config_server_addr=${nacos_config_server_addr}
# mq type: talos/rocketmq
## es
es.bulk_actions=${es.bulk_actions}
es.byte_size=${es.byte_size}
es.concurrent_request=${es.concurrent_request}
es.flush_interval=${es.flush_interval}
es.retry_num=${es.retry_num}
es.retry_interval=${es.retry_interval}
## other
nacosAddress=${nacos_config_server_addr}
log.path=${log.path}

localIp=${localIp}

rocketmq_group=${rocketmq_group}
rocketmq_producer_topic=${rocketmq_producer_topic}

# configuration related to message compensation mechanism
hera.stream.compensate.enable=${hera.stream.compensate.enable}
hera.stream.compensate.mq=${hera.stream.compensate.mq}

# monitor nacos data id
hera.stream.monitor_space_data_id=${hera.stream.monitor_space_data_id}
# normal log or backup log
sink_job_type=${sink_job_type}

kafka.use.ssl=true
kafka.sll.location=/opt/app/mix.4096.client.truststore.jks

