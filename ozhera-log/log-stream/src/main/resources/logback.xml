<?xml version="1.0" encoding="UTF-8" ?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<configuration scan="false" debug="false">
    <property resource="config.properties"></property>

    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>%d|%-5level|%X{trace_id}|%thread|%logger{40}|%L|%msg%n</pattern>
        </layout>
    </appender>

    <appender name="logfile"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${log.path}/server.log</File>
        <encoder>
            <pattern>%d|%-5level|%X{trace_id}|%thread|%logger{40}|%L|%msg%n</pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${log.path}/server.log.%d{yyyy-MM-dd-HH}</FileNamePattern>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <maxHistory>168</maxHistory>
        </rollingPolicy>
<!--        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">-->
<!--            <fileNamePattern>${log.path}/server.log.%d{yyyy-MM-dd-HH}-%i</fileNamePattern>-->
<!--            <maxFileSize>1M</maxFileSize>-->
<!--            <maxHistory>60</maxHistory>-->
<!--            <totalSizeCap>1GB</totalSizeCap>-->
<!--        </rollingPolicy>-->

    </appender>

    <!--    <appender name="asyncLogFile" class="ch.qos.logback.classic.AsyncAppender">-->
    <!--        <queueSize>5024</queueSize>-->
    <!--        <appender-ref ref="logfile"/>-->
    <!--    </appender>-->


    <logger name="org.springframework" level="ERROR"/>
    <logger name="ch.qos.logback" level="ERROR"/>
    <logger name="com.xiaomi.data.push.service.state" level="ERROR"/>
    <logger name="org.reflections.Reflections" level="ERROR"/>
    <logger name="com.xiaomi.infra.galaxy" level="ERROR"/>

    <root level="INFO">
        <appender-ref ref="stdout"/>
        <appender-ref ref="logfile"/>
    </root>


</configuration>