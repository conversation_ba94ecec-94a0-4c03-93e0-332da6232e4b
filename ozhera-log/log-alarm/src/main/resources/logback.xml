<?xml version="1.0" encoding="UTF-8" ?>
<configuration scan="false" debug="false">
    <property resource="config.properties"></property>

    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>%d|%-5level|%X{trace_id}|%thread|%logger{40}|%L|%msg%n</pattern>
        </layout>
    </appender>

    <appender name="logfile"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${log.path}/server.log</File>
        <encoder>
            <pattern>%d|%-5level|%X{trace_id}|%thread|%logger{40}|%L|%msg%n</pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${log.path}/server.log.%d{yyyy-MM-dd-HH}</FileNamePattern>
            <maxHistory>148</maxHistory>
        </rollingPolicy>
    </appender>

    <!--    <appender name="asyncLogFile" class="ch.qos.logback.classic.AsyncAppender">-->
    <!--        <queueSize>5024</queueSize>-->
    <!--        <appender-ref ref="logfile"/>-->
    <!--    </appender>-->


    <logger name="org.springframework" level="ERROR"/>
    <logger name="ch.qos.logback" level="ERROR"/>
    <logger name="com.xiaomi.data.push.service.state" level="ERROR"/>
    <logger name="org.reflections.Reflections" level="ERROR"/>
    <logger name="com.xiaomi.infra.galaxy" level="ERROR"/>

    <root level="INFO">
        <appender-ref ref="stdout"/>
        <appender-ref ref="logfile"/>
    </root>


</configuration>