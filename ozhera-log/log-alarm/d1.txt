[INFO] Scanning for projects...
[INFO] 
[INFO] ---------------------< com.xiaomi.mone:log-alarm >----------------------
[INFO] Building log-alarm 1.0-SNAPSHOT
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- maven-dependency-plugin:2.8:tree (default-cli) @ log-alarm ---
[INFO] com.xiaomi.mone:log-alarm:jar:1.0-SNAPSHOT
[INFO] +- ch.qos.logback:logback-classic:jar:1.3.4:compile
[INFO] |  +- ch.qos.logback:logback-core:jar:1.3.4:compile
[INFO] |  +- org.slf4j:slf4j-api:jar:2.0.1:compile
[INFO] |  \- com.sun.mail:javax.mail:jar:1.6.2:runtime
[INFO] |     \- javax.activation:activation:jar:1.1:runtime
[INFO] +- org.apache.logging.log4j:log4j-slf4j-impl:jar:2.17.0:compile
[INFO] |  +- org.apache.logging.log4j:log4j-api:jar:2.17.0:compile
[INFO] |  \- org.apache.logging.log4j:log4j-core:jar:2.17.0:runtime
[INFO] +- com.xiaomi.infra.galaxy:galaxy-talos-sdk:jar:2.7.0:compile
[INFO] |  +- com.xiaomi.infra.galaxy:galaxy-lcs-common:jar:2.1.13:compile
[INFO] |  |  +- com.xiaomi.infra.galaxy:galaxy-lcs-thrift:jar:2.1.13:compile
[INFO] |  |  +- com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:jar:1.4.2:compile
[INFO] |  |  +- dnsjava:dnsjava:jar:2.1.8:compile
[INFO] |  |  +- org.json:json:jar:20090211:compile
[INFO] |  |  +- com.twitter.common:util:jar:0.0.92:compile
[INFO] |  |  |  +- com.twitter.common:util-executor-service-shutdown:jar:0.0.47:compile
[INFO] |  |  |  +- com.twitter.common:jdk-logging:jar:0.0.41:compile
[INFO] |  |  |  +- com.twitter.common:base:jar:0.0.82:compile
[INFO] |  |  |  |  \- com.twitter.common:util-system-mocks:jar:0.0.67:compile
[INFO] |  |  |  +- com.twitter.common:collections:jar:0.0.69:compile
[INFO] |  |  |  +- com.twitter.common:quantity:jar:0.0.66:compile
[INFO] |  |  |  +- com.twitter.common:stats:jar:0.0.89:compile
[INFO] |  |  |  |  +- com.twitter.common:stat-registry:jar:0.0.24:compile
[INFO] |  |  |  |  |  \- com.twitter.common:stat:jar:0.0.26:compile
[INFO] |  |  |  |  +- com.twitter.common:stats-provider:jar:0.0.53:compile
[INFO] |  |  |  |  +- com.twitter.common:application-action:jar:0.0.66:compile
[INFO] |  |  |  |  |  \- com.google.inject:guice:jar:3.0:compile
[INFO] |  |  |  |  |     \- aopalliance:aopalliance:jar:1.0:compile
[INFO] |  |  |  |  \- com.twitter.common:util-sampler:jar:0.0.50:compile
[INFO] |  |  |  +- commons-lang:commons-lang:jar:2.6:compile
[INFO] |  |  |  \- javax.inject:javax.inject:jar:1:compile
[INFO] |  |  \- javax.servlet:javax.servlet-api:jar:3.0.1:compile
[INFO] |  +- com.xiaomi.infra.galaxy:galaxy-lcs-metric-lib:jar:2.1.13:compile
[INFO] |  |  +- org.apache.thrift:thrift:jar:0.5.0-fix-thrift2402:compile
[INFO] |  |  \- com.codahale.metrics:metrics-core:jar:3.0.2:compile
[INFO] |  +- com.xiaomi.infra.galaxy:galaxy-client-java:jar:1.2.5:compile
[INFO] |  |  \- com.fasterxml.uuid:java-uuid-generator:jar:3.1.3:compile
[INFO] |  +- com.xiaomi.infra.galaxy:galaxy-thrift-api:jar:1.2.8:compile
[INFO] |  +- org.xerial.snappy:snappy-java:jar:1.1.2.6:compile
[INFO] |  +- com.github.luben:zstd-jni:jar:1.5.0-4:compile
[INFO] |  +- org.lz4:lz4-java:jar:1.8.0:compile
[INFO] |  +- org.apache.httpcomponents:httpclient:jar:4.2.2:compile
[INFO] |  +- org.apache.httpcomponents:httpcore:jar:4.2.2:compile
[INFO] |  +- com.xiaomi.infra.galaxy:galaxy-schema-client:jar:1.0.0:compile
[INFO] |  |  +- com.xiaomi.infra.galaxy:streaming-common:jar:1.0:compile
[INFO] |  |  +- com.xiaomi.infra.galaxy:streaming-http:jar:1.0:compile
[INFO] |  |  +- org.apache.thrift:libthrift:jar:0.9.2:compile
[INFO] |  |  +- org.apache.avro:avro:jar:1.8.2:compile
[INFO] |  |  |  +- org.codehaus.jackson:jackson-core-asl:jar:1.9.13:compile
[INFO] |  |  |  +- org.codehaus.jackson:jackson-mapper-asl:jar:1.9.13:compile
[INFO] |  |  |  +- com.thoughtworks.paranamer:paranamer:jar:2.7:compile
[INFO] |  |  |  \- org.tukaani:xz:jar:1.5:compile
[INFO] |  |  +- org.apache.avro:avro-thrift:jar:1.8.2:compile
[INFO] |  |  \- org.apache.avro:avro-protobuf:jar:1.8.2:compile
[INFO] |  |     \- com.google.protobuf:protobuf-java:jar:2.5.0:compile
[INFO] |  +- com.xiaomi.infra.galaxy:galaxy-schema-thrift:jar:1.0.0:compile
[INFO] |  +- commons-beanutils:commons-beanutils:jar:1.9.4:compile
[INFO] |  |  \- commons-collections:commons-collections:jar:3.2.2:compile
[INFO] |  \- org.jsoup:jsoup:jar:1.13.1:compile
[INFO] +- org.apache.flink:flink-table-common:jar:1.14.0-mdh1.14.0.0-SNAPSHOT:compile
[INFO] |  +- org.apache.flink:flink-core:jar:1.14.0-mdh1.14.0.0-SNAPSHOT:compile
[INFO] |  |  +- org.apache.flink:flink-annotations:jar:1.14.0-mdh1.14.0.0-SNAPSHOT:compile
[INFO] |  |  +- org.apache.flink:flink-metrics-core:jar:1.14.0-mdh1.14.0.0-SNAPSHOT:compile
[INFO] |  |  +- com.esotericsoftware.kryo:kryo:jar:2.24.0:compile
[INFO] |  |  |  +- com.esotericsoftware.minlog:minlog:jar:1.2:compile
[INFO] |  |  |  \- org.objenesis:objenesis:jar:2.1:compile
[INFO] |  |  \- org.apache.commons:commons-compress:jar:1.21:compile
[INFO] |  +- org.apache.flink:flink-connector-files:jar:1.14.0-mdh1.14.0.0-SNAPSHOT:compile
[INFO] |  |  \- org.apache.flink:flink-connector-base:jar:1.14.0-mdh1.14.0.0-SNAPSHOT:compile
[INFO] |  +- org.apache.flink:flink-shaded-asm-7:jar:7.1-14.0:compile
[INFO] |  +- com.google.code.findbugs:jsr305:jar:1.3.9:compile
[INFO] |  \- org.apache.flink:flink-shaded-force-shading:jar:14.0:compile
[INFO] +- org.apache.flink:flink-connector-talos:jar:1.14.0-mdh1.14.0.0-SNAPSHOT:compile
[INFO] |  +- com.xiaomi.infra.galaxy:galaxy-lcs-monitor-client:jar:4.1.7:compile
[INFO] |  \- org.apache.flink:flink-metrics-dropwizard:jar:1.14.0-mdh1.14.0.0-SNAPSHOT:compile
[INFO] |     \- io.dropwizard.metrics:metrics-core:jar:3.2.6:compile
[INFO] +- org.apache.flink:flink-java:jar:1.14.0-mdh1.14.0.0-SNAPSHOT:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.3.2:compile
[INFO] |  \- org.apache.commons:commons-math3:jar:3.5:compile
[INFO] +- org.apache.flink:flink-streaming-java_2.12:jar:1.14.0-mdh1.14.0.0-SNAPSHOT:compile
[INFO] |  +- org.apache.flink:flink-file-sink-common:jar:1.14.0-mdh1.14.0.0-SNAPSHOT:compile
[INFO] |  +- org.apache.flink:flink-runtime:jar:1.14.0-mdh1.14.0.0-SNAPSHOT:compile
[INFO] |  |  +- org.apache.flink:flink-rpc-core:jar:1.14.0-mdh1.14.0.0-SNAPSHOT:compile
[INFO] |  |  +- org.apache.flink:flink-rpc-akka-loader:jar:1.14.0-mdh1.14.0.0-SNAPSHOT:compile
[INFO] |  |  +- org.apache.flink:flink-queryable-state-client-java:jar:1.14.0-mdh1.14.0.0-SNAPSHOT:compile
[INFO] |  |  +- org.apache.flink:flink-hadoop-fs:jar:1.14.0-mdh1.14.0.0-SNAPSHOT:compile
[INFO] |  |  +- commons-io:commons-io:jar:2.8.0:compile
[INFO] |  |  +- org.apache.flink:flink-shaded-zookeeper-3:jar:3.4.14-14.0:compile
[INFO] |  |  +- org.javassist:javassist:jar:3.24.0-GA:compile
[INFO] |  |  \- org.apache.logging.log4j:log4j-1.2-api:jar:2.17.0:compile
[INFO] |  +- org.apache.flink:flink-scala_2.12:jar:1.14.0-mdh1.14.0.0-SNAPSHOT:compile
[INFO] |  |  +- org.scala-lang:scala-reflect:jar:2.12.7:compile
[INFO] |  |  +- org.scala-lang:scala-library:jar:2.12.7:compile
[INFO] |  |  +- org.scala-lang:scala-compiler:jar:2.12.7:compile
[INFO] |  |  |  \- org.scala-lang.modules:scala-xml_2.12:jar:1.0.6:compile
[INFO] |  |  \- com.twitter:chill_2.12:jar:0.7.6:compile
[INFO] |  |     \- com.twitter:chill-java:jar:0.7.6:compile
[INFO] |  \- org.apache.flink:flink-shaded-guava:jar:30.1.1-jre-14.0:compile
[INFO] +- org.apache.flink:flink-clients_2.12:jar:1.14.0-mdh1.14.0.0-SNAPSHOT:compile
[INFO] |  +- org.apache.flink:flink-optimizer:jar:1.14.0-mdh1.14.0.0-SNAPSHOT:compile
[INFO] |  \- commons-cli:commons-cli:jar:1.3.1:compile
[INFO] +- org.apache.flink:flink-runtime-web_2.12:jar:1.14.0-mdh1.14.0.0-SNAPSHOT:compile
[INFO] |  +- org.apache.flink:flink-shaded-netty:jar:4.1.65.Final-14.0:compile
[INFO] |  \- org.apache.flink:flink-shaded-jackson:jar:2.12.4-14.0:compile
[INFO] +- run.mone:log-common:jar:1.1-SNAPSHOT:compile
[INFO] |  +- com.belerweb:pinyin4j:jar:2.5.1:compile
[INFO] |  +- com.gliwka.hyperscan:hyperscan:jar:5.4.0-2.0.0:compile
[INFO] |  |  +- com.gliwka.hyperscan:native:jar:5.4.0-1.0.0:compile
[INFO] |  |  +- com.gliwka.hyperscan:native:jar:linux-x86_64:5.4.0-1.0.0:compile
[INFO] |  |  +- com.gliwka.hyperscan:native:jar:windows-x86_64:5.4.0-1.0.0:compile
[INFO] |  |  +- com.gliwka.hyperscan:native:jar:macosx-x86_64:5.4.0-1.0.0:compile
[INFO] |  |  +- org.bytedeco:javacpp:jar:1.5.4:compile
[INFO] |  |  \- org.bytedeco:javacpp-platform:jar:1.5.4:compile
[INFO] |  |     +- org.bytedeco:javacpp:jar:android-arm:1.5.4:compile
[INFO] |  |     +- org.bytedeco:javacpp:jar:android-arm64:1.5.4:compile
[INFO] |  |     +- org.bytedeco:javacpp:jar:android-x86:1.5.4:compile
[INFO] |  |     +- org.bytedeco:javacpp:jar:android-x86_64:1.5.4:compile
[INFO] |  |     +- org.bytedeco:javacpp:jar:ios-arm64:1.5.4:compile
[INFO] |  |     +- org.bytedeco:javacpp:jar:ios-x86_64:1.5.4:compile
[INFO] |  |     +- org.bytedeco:javacpp:jar:linux-armhf:1.5.4:compile
[INFO] |  |     +- org.bytedeco:javacpp:jar:linux-arm64:1.5.4:compile
[INFO] |  |     +- org.bytedeco:javacpp:jar:linux-ppc64le:1.5.4:compile
[INFO] |  |     +- org.bytedeco:javacpp:jar:linux-x86:1.5.4:compile
[INFO] |  |     +- org.bytedeco:javacpp:jar:linux-x86_64:1.5.4:compile
[INFO] |  |     +- org.bytedeco:javacpp:jar:macosx-x86_64:1.5.4:compile
[INFO] |  |     +- org.bytedeco:javacpp:jar:windows-x86:1.5.4:compile
[INFO] |  |     \- org.bytedeco:javacpp:jar:windows-x86_64:1.5.4:compile
[INFO] |  +- com.google.code.gson:gson:jar:2.10.1:compile
[INFO] |  +- com.google.guava:guava:jar:32.1.1-jre:compile
[INFO] |  |  +- com.google.guava:failureaccess:jar:1.0.1:compile
[INFO] |  |  +- com.google.guava:listenablefuture:jar:9999.0-empty-to-avoid-conflict-with-guava:compile
[INFO] |  |  +- org.checkerframework:checker-qual:jar:3.33.0:compile
[INFO] |  |  +- com.google.errorprone:error_prone_annotations:jar:2.18.0:compile
[INFO] |  |  \- com.google.j2objc:j2objc-annotations:jar:2.8:compile
[INFO] |  +- org.apache.kafka:kafka-clients:jar:2.6.3:compile
[INFO] |  \- com.google.auto.service:auto-service-annotations:jar:1.0.1:compile
[INFO] +- run.mone:log-api:jar:1.1-SNAPSHOT:compile
[INFO] +- com.xiaomi.youpin.mione:rocketmq-flink:jar:1.0-SNAPSHOT:compile
[INFO] |  +- commons-validator:commons-validator:jar:1.7:compile
[INFO] |  |  +- commons-digester:commons-digester:jar:2.1:compile
[INFO] |  |  \- commons-logging:commons-logging:jar:1.2:compile
[INFO] |  +- org.apache.flink:flink-streaming-java_2.11:jar:1.12.2-mdh1.12.2.0:compile
[INFO] |  |  \- org.apache.flink:flink-runtime_2.11:jar:1.12.2-mdh1.12.2.0:compile
[INFO] |  |     +- com.typesafe.akka:akka-actor_2.11:jar:2.5.21:compile
[INFO] |  |     |  +- com.typesafe:config:jar:1.3.3:compile
[INFO] |  |     |  \- org.scala-lang.modules:scala-java8-compat_2.11:jar:0.7.0:compile
[INFO] |  |     +- com.typesafe.akka:akka-stream_2.11:jar:2.5.21:compile
[INFO] |  |     |  +- org.reactivestreams:reactive-streams:jar:1.0.2:compile
[INFO] |  |     |  \- com.typesafe:ssl-config-core_2.11:jar:0.3.7:compile
[INFO] |  |     |     \- org.scala-lang.modules:scala-parser-combinators_2.11:jar:1.1.1:compile
[INFO] |  |     +- com.typesafe.akka:akka-protobuf_2.11:jar:2.5.21:compile
[INFO] |  |     +- com.typesafe.akka:akka-slf4j_2.11:jar:2.5.21:compile
[INFO] |  |     +- org.clapper:grizzled-slf4j_2.11:jar:1.3.2:compile
[INFO] |  |     +- com.github.scopt:scopt_2.11:jar:3.5.0:compile
[INFO] |  |     \- com.twitter:chill_2.11:jar:0.7.6:compile
[INFO] |  \- org.apache.flink:flink-clients_2.11:jar:1.12.2-mdh1.12.2.0:compile
[INFO] |     \- org.apache.flink:flink-optimizer_2.11:jar:1.12.2-mdh1.12.2.0:compile
[INFO] +- com.xiaomi.infra:rocketmq-client-java:jar:1.0.4-RELEASE:compile
[INFO] |  +- org.apache.rocketmq:rocketmq-client:jar:4.8.0-mdh4.8.2-RELEASE:compile
[INFO] |  |  +- org.apache.rocketmq:rocketmq-common:jar:4.8.0-mdh4.8.2-RELEASE:compile
[INFO] |  |  |  \- org.apache.rocketmq:rocketmq-remoting:jar:4.8.0-mdh4.8.2-RELEASE:compile
[INFO] |  |  |     \- io.netty:netty-tcnative-boringssl-static:jar:1.1.33.Fork26:compile
[INFO] |  |  \- io.prometheus.jmx:jmx_prometheus_javaagent:jar:0.13.0:compile
[INFO] |  \- org.apache.rocketmq:rocketmq-tools:jar:4.8.0-mdh4.8.2-RELEASE:compile
[INFO] |     +- org.apache.rocketmq:rocketmq-acl:jar:4.8.0-mdh4.8.2-RELEASE:compile
[INFO] |     |  +- org.apache.rocketmq:rocketmq-logging:jar:4.8.0-mdh4.8.2-RELEASE:compile
[INFO] |     |  +- org.apache.rocketmq:rocketmq-metamanager:jar:4.8.0-mdh4.8.2-RELEASE:compile
[INFO] |     |  |  \- org.apache.commons:commons-dbcp2:jar:2.7.0:compile
[INFO] |     |  +- com.xiaomi.infra.galaxy:cloud-authentication:jar:1.3.9.5:compile
[INFO] |     |  |  +- com.xiaomi.infra.galaxy:cloud-authentication-core:jar:1.3.9.5:compile
[INFO] |     |  |  +- xerces:xercesImpl:jar:2.9.1:compile
[INFO] |     |  |  |  \- xml-apis:xml-apis:jar:1.3.04:compile
[INFO] |     |  |  \- com.xiaomi.common.perfcounter:xiaomi-common-perfcounter:jar:2.5.13:compile
[INFO] |     |  \- com.xiaomi:keycenter-agent-client:jar:2.1.12:compile
[INFO] |     |     +- org.apache.commons:commons-pool2:jar:2.4.2:compile
[INFO] |     |     +- com.xiaomi:xiaomi-common-utils:jar:2.7.4:compile
[INFO] |     |     +- com.yammer.metrics:metrics-core:jar:2.2.0:compile
[INFO] |     |     +- org.aspectj:aspectjrt:jar:1.8.4:compile
[INFO] |     |     \- org.aspectj:aspectjweaver:jar:1.8.4:compile
[INFO] |     +- org.apache.rocketmq:rocketmq-store:jar:4.8.0-mdh4.8.2-RELEASE:compile
[INFO] |     |  \- io.openmessaging.storage:dledger:jar:0.2.2:compile
[INFO] |     |     \- com.beust:jcommander:jar:1.72:compile
[INFO] |     \- org.apache.rocketmq:rocketmq-srvutil:jar:4.8.0-mdh4.8.2-RELEASE:compile
[INFO] +- mysql:mysql-connector-java:jar:5.1.48:compile
[INFO] +- org.apache.flink:flink-jdbc_2.12:jar:1.10.1:compile
[INFO] |  \- org.apache.flink:force-shading:jar:1.10.1:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.0.0-M6:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.0.0-M3:test
[INFO] |  \- org.junit.platform:junit-platform-commons:jar:1.0.0-M6:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.0.0-M6:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.0.0-M6:test
[INFO] +- org.junit.jupiter:junit-jupiter-params:jar:5.0.0-M6:test
[INFO] +- net.java.dev.jna:jna:jar:4.4.0:compile
[INFO] +- cn.hutool:hutool-all:jar:5.8.20:compile
[INFO] +- com.alibaba.nacos:nacos-client:jar:1.2.1-mone-v6-SNAPSHOT:compile
[INFO] |  +- com.xiaomi.youpin:rpc:jar:1.7-SNAPSHOT:compile
[INFO] |  |  +- io.netty:netty-all:jar:4.1.48.Final:compile
[INFO] |  |  \- org.msgpack:jackson-dataformat-msgpack:jar:0.8.18:compile
[INFO] |  |     \- org.msgpack:msgpack-core:jar:0.8.18:compile
[INFO] |  +- com.alibaba.nacos:nacos-common:jar:1.2.1-mone-v6-SNAPSHOT:compile
[INFO] |  +- com.alibaba.nacos:nacos-api:jar:1.2.1-mone-v6-SNAPSHOT:compile
[INFO] |  +- com.alibaba:dubbo-registry-nacos:jar:1.2.1-mone-SNAPSHOT:compile
[INFO] |  +- commons-codec:commons-codec:jar:1.11:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-core:jar:2.9.10:compile
[INFO] |  +- io.prometheus:simpleclient:jar:0.5.0:compile
[INFO] |  \- org.yaml:snakeyaml:jar:1.23:compile
[INFO] +- com.fasterxml.jackson.core:jackson-databind:jar:2.9.10.8:compile
[INFO] |  \- com.fasterxml.jackson.core:jackson-annotations:jar:2.9.10:compile
[INFO] +- junit:junit:jar:4.12:test
[INFO] |  \- org.hamcrest:hamcrest-core:jar:1.3:test
[INFO] \- org.projectlombok:lombok:jar:1.18.30:compile
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  2.431 s
[INFO] Finished at: 2025-04-07T15:00:52+08:00
[INFO] ------------------------------------------------------------------------
