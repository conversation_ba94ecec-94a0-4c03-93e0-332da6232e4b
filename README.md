<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->

# Apache OzHera(Incubating)

<p align="center">
<b>Apache OzHera(Incubating):Application Observable Platform in the Cloud Native Era</b>
</p>

<p align="center">
<a href="README_CN.md"><img src="./readme/images/doc_logo_cn.svg" alt="CN doc"></a>
<a href="README.md"><img src="./readme/images/doc_logo_english.svg" alt="EN doc"></a>
</p>


## What is Apache OzHera(Incubating)?
Apache OzHera(Incubating) is an Application Observable Platform in the Cloud Native Era. Centered around applications, it integrates metrics monitoring, link tracing, logging, and alerts. It achieves a seamless connection and interaction from metrics to tracing to logging. Moreover, Apache OzHera(Incubating) offers a comprehensive monitoring dashboard featuring application health status lists, application metric boards, interface overviews, application overviews, gateway overviews, etc., along with clear and concise visual text alerts, enabling users to pinpoint issues accurately and efficiently.

---

## Architecture
![ozhera](./readme/images/architecture.png)

---

## Features
- Accurate: Extract usability metrics based on business error codes.
- Fast: Integrated metrics-tracing-logging.
- Economical: <0.1% storage cost, meets 99.9% of tracing demands.
- Cloud-native Friendly: Adheres to the Opentracing standard, deeply adapts to K8S, and integrates multiple star open-source products like Opentelemetry, Grafana, Prometheus, ES, and more.
- Enterprise-level observability product.

---

## Getting Started
### Live Demo
+ [Live Demo](https://ozhera.demo.m.one.mi.com/)
+ username: <EMAIL>
+ password: 123456

---

### Official Website

Welcome to visit the [official website](https://ozhera.apache.org/) of Apache OzHera(Incubating).

---

### Deployment
[operator-usage-document.md](readme%2Fdeploy%2Fozhera-deploy-document.md)

---

### Application Integration
[application-integration-document.md](readme/application-integeration/application-integration-document.md)

---

### User Manual
[user-manual-document.md](readme/user-manual/user-manual-document.md)

---

### Contributing
Contributors are welcomed to join ozhera project. Please check [contributing.md](CONTRIBUTING.md) about how to contribute to this project.

How can I contribute?
- Take a look at issues with tags marked [good first issue](https://github.com/apache/ozhera/labels/good%20first%20issue) or [contribution welcome](https://github.com/apache/ozhera/labels/help%20wanted).
- Answer questions on [issues](https://github.com/apache/ozhera/issues).
- Fix bugs reported on [issues](https://github.com/apache/ozhera/issues), and send us a pull request.
- Review the existing [pull request](https://github.com/apache/ozhera/pulls).

---

### Contact Us

We warmly welcome your valuable opinions and suggestions about the project. Whether you have technical questions, feature requests, or wish to join our development team, please feel free to contact us through the following methods:

#### Email Contact
- **Email Address**: [<EMAIL>](mailto:<EMAIL>)
- **Response Time**: We will respond to your email within 24 hours of receipt.

#### Community Engagement
- We also welcome you to join our community for discussions and exchanges:
    - Welcome to visit the [community website](https://ozhera.apache.org) of Apache OzHera(Incubating), where we will publish Apache OzHera(Incubating)'s technical documentation, thoughts, and achievements. We welcome everyone to join the discussion.
    - [GitHub Discussions](https://github.com/apache/ozhera/issues?q=is%3Aissue+is%3Aopen+%5BDisscusion%5D+)

---





