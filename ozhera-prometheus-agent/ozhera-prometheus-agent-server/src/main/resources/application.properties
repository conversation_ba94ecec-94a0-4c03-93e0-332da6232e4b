# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#server
app.name=prometheus-agent
server.type=${server.type}
server.port=${server.port}
server.debug=true
server.connection-timeout=1000

dubbo.group=${dubbo.group}
dubbo.protocol.id=${dubbo.protocol.id}
dubbo.protocol.name=${dubbo.protocol.name}
dubbo.protocol.port=${dubbo.protocol.port}
nacos.config.addrs=${nacos.config.addrs}

log.path=${log.path}

service.selector.property=${service.selector.property}

vm.scrape.job.path=${vm.scrape.job.path}
vm.alert.rule.path=${vm.alert.rule.path}