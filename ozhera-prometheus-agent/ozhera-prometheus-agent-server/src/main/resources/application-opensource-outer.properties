# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#server
app.name=prometheus-agent
server.type=opensource-outer
server.port=8080
server.debug=true
server.connection-timeout=1000

dubbo.group=opensource-outer
dubbo.protocol.id=dubbo
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
nacos.config.addrs=nacos:80

log.path=/home/<USER>/log

service.selector.property=outer

vm.scrape.job.path=/tmp/vm/scrape_job.yaml
vm.alert.rule.path=/tmp/vm/alert.rule