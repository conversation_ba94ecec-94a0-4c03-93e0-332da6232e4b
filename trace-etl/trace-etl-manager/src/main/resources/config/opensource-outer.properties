# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#server
app.name=trace_etl_manager
server.type=staging
server.port=8080
server.debug=true
server.connection-timeout=1000

dubbo.group=staging
dubbo.protocol.id=dubbo
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1

log.path=/home/<USER>/log

nacos.address=nacos:80

es.trace.index.prefix=mione-staging-zgq-jaeger-span
es.trace.index.service.prefix=mione-staging-zgq-jaeger-service
es.trace.index.driver.prefix=mione-staging-zgq-driver-
es.error.index=zgq_common_staging_private_prometheus-

redis.is.open=false