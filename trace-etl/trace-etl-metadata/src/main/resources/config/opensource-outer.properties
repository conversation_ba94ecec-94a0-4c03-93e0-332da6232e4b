# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#server
app.name=trace-etl
server.type=staging
server.port=8085
server.debug=true
server.connection-timeout=1000

dubbo.group=staging
dubbo.protocol.id=dubbo
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.registry.address=nacos://nacos:80

log.path=/home/<USER>/log

nacos.address=nacos:80

redis.is.open=true
spring.redis.jedis.pool.max-active=30
spring.redis.pool.max-wait=1000
spring.redis.pool.max-idle=10
spring.redis.jedis.pool.min-idle=0
spring.redis.timeout.connection=1000
spring.redis.max-attempts=5

# RocketMQ topic
mq.consumer.topic=hera_meta_data_staging
mq.consumer.group=hera_meta_data_staging