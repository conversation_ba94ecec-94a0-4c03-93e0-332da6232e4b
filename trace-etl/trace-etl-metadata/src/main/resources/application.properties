# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#server
app.name=${app.name}
server.type=${server.type}
server.port=${server.port}
server.debug=true
server.connection-timeout=1000

dubbo.group=${dubbo.group}
dubbo.protocol.id=${dubbo.protocol.id}
dubbo.protocol.name=${dubbo.protocol.name}
dubbo.protocol.port=${dubbo.protocol.port}
dubbo.registry.address=${dubbo.registry.address}

log.path=${log.path}

nacos.address=${nacos.address}

redis.is.open=${redis.is.open}
spring.redis.jedis.pool.max-active=${spring.redis.jedis.pool.max-active}
spring.redis.pool.max-wait=${spring.redis.pool.max-wait}
spring.redis.pool.max-idle=${spring.redis.pool.max-idle}
spring.redis.jedis.pool.min-idle=${spring.redis.jedis.pool.min-idle}
spring.redis.timeout.connection=${spring.redis.timeout.connection}
spring.redis.max-attempts=${spring.redis.max-attempts}

spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.default.initialPoolSize=10
spring.datasource.default.maxPoolSize=20
spring.datasource.default.minialPoolSize=10

mq.type=rocketMQ
mq.consumer.topic=${mq.consumer.topic}
mq.consumer.group=${mq.consumer.group}