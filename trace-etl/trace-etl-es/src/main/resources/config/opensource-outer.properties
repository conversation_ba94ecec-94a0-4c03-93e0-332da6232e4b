# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#server
app.name=trace_etl_es
server.type=staging
server.port=8080
server.debug=true
server.connection-timeout=1000

dubbo.group=staging
dubbo.protocol.id=dubbo
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1

log.path=/home/<USER>/log

nacos.address=nacos:80

es.error.index=zgq_common_staging_private_prometheus-
es.trace.index.prefix=mione-staging-zgq-jaeger-span-
es.trace.index.service.prefix=mione-staging-zgq-jaeger-service-
es.trace.index.driver.prefix=mione-staging-zgq-driver-

redis.is.open=true
spring.redis.jedis.pool.max-active=20
spring.redis.pool.max-wait=30000
spring.redis.pool.max-idle=10
spring.redis.jedis.pool.min-idle=0
spring.redis.timeout.connection=5000
spring.redis.max-attempts=5

rocks.first.gap=5
rocks.second.gap=30
rocks.first.path=/home/<USER>/first
rocks.second.path=/home/<USER>/second


mq.es.topic=mone_hera_staging_trace_etl_es
mq.consumer.group=trace-etl-es
