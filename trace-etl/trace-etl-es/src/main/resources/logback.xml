<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->

<configuration scan="true" scanPeriod="5 seconds">

    <property resource="application.properties"></property>

    <property name="MAX_HISTORY" value="60"/>

    <appender name="fileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/trace-etl-es/server.log</file>
        <encoder>
            <pattern>%d|%-5level|%thread|%logger{40}|%L|%msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/trace-etl-es/server.log.%d{yyyy-MM-dd-HH}</fileNamePattern>
            <MaxHistory>${MAX_HISTORY}</MaxHistory>
        </rollingPolicy>
    </appender>

    <appender name="asyncAppender" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>60000</queueSize>
        <appender-ref ref="fileAppender"/>
    </appender>


    <appender name="errorAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/trace-etl-es/error.log</file>
        <encoder>
            <pattern>%d|%-5level|%thread|%logger{40}|%L|%msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/trace-etl-es/error.log.%d{yyyy-MM-dd-HH}</fileNamePattern>
            <MaxHistory>${MAX_HISTORY}</MaxHistory>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d|%-5level|%thread|%logger{40}|%L|%msg%n</pattern>
        </encoder>
    </appender>

    <logger name="org.springframework" level="ERROR"/>
    <logger name="ch.qos.logback" level="ERROR"/>
    <logger name="org.reflections.Reflections" level="ERROR"/>

    <root level="INFO">
        <appender-ref ref="stdout"/>
        <appender-ref ref="errorAppender"/>
        <appender-ref ref="fileAppender"/>
    </root>

</configuration>