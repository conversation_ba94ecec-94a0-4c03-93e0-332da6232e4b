# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#server
app.name=${app.name}
server.type=${server.type}
server.port=${server.port}
server.debug=true
server.connection-timeout=1000

dubbo.group=${dubbo.group}
dubbo.protocol.id=${dubbo.protocol.id}
dubbo.protocol.name=${dubbo.protocol.name}
dubbo.protocol.port=${dubbo.protocol.port}

log.path=${log.path}

nacos.address=${nacos.address}

prometheus.http.server.port=${prometheus.http.server.port}
metrics.uri.whitelist=${metrics.uri.whitelist}

es.domain=${es.domain}
es.error.index=${es.error.index}
es.trace.index.prefix=${es.trace.index.prefix}
es.trace.index.service.prefix=${es.trace.index.service.prefix}
es.trace.index.driver.prefix=${es.trace.index.driver.prefix}

security.scanner.ua=${security.scanner.ua}

spring.datasource.default.initialPoolSize=10
spring.datasource.default.maxPoolSize=20
spring.datasource.default.minialPoolSize=10

redis.is.open=${redis.is.open}

mq.server.topic=${mq.server.topic}
mq.es.topic=${mq.es.topic}
mq.producer.group=${mq.producer.group}
mq.consumer.group=${mq.consumer.group}

mq.type=kafka
storage.type=es

spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration