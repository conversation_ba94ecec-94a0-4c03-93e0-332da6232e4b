# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

server.type=@server.type@
server.port=@server.port@
app.name =@app.name@
#app.nacos=@app.nacos@

# dubbo
dubbo.protocol.port=@dubbo.protocol.port@
dubbo.registry.address=@dubbo.registry.address@

#dubbo 分组配置
dubbo.group=@dubbo.group@
dubbo.group.miline=@dubbo.group.miline@
dubbo.group.heraapp=@dubbo.group.heraapp@
dubbo.group.alert=@dubbo.group.alert@
dubbo.group.tpc=@dubbo.group.tpc@

#nacos 配置地址
nacos.config.addrs=@nacos.config.addrs@


#RestTemplateConfig
resttemplate.connection.timeout=@resttemplate.connection.timeout@
resttemplate.read.timeout=@resttemplate.read.timeout@


log.path=@log.path@

dev.mode=@dev.mode@
inner.auth=@inner.auth@

service.selector.property=@service.selector.property@
spring.main.allow-circular-references=true
metric.detail.datasource.property=@metric.detail.datasource.property@
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration