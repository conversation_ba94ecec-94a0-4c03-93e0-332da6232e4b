<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->

<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <style>
        html,
        body {
            height: 100%;
        }

        body {
            background-color: #f2f2f2;
            color: #444;
            font: 12px/1.5 'Helvetica Neue', Arial, Helvetica, sans-serif;
            background: url("img/blueprint.png") repeat 0 0;
        }

        div.da-wrapper {
            width: 100%;
            height: auto;
            min-height: 100%;
            position: relative;
            min-width: 320px
        }

        div.da-wrapper .da-container {
            width: 96%;
            margin: auto
        }

        div.da-content {
            clear: both;
            padding-bottom: 58px
        }

        @media only screen and (max-width:480px) {
            div.da-content {
                margin-top: auto
            }
        }

        div.da-error-wrapper {
            width: 320px;
            padding: 30px 0;
            margin: auto;
            position: relative
        }

        div.da-error-wrapper .da-error-heading {
            color: #e15656;
            text-align: center;
            font-size: 24px;
            font-family: Georgia, "Times New Roman", Times, serif
        }

        @-webkit-keyframes error-swing {
            0% {
                -webkit-transform: rotate(1deg)
            }

            100% {
                -webkit-transform: rotate(-2deg)
            }
        }

        @-moz-keyframes error-swing {
            0% {
                -moz-transform: rotate(1deg)
            }

            100% {
                -moz-transform: rotate(-2deg)
            }
        }

        @keyframes error-swing {
            0% {
                transform: rotate(1deg)
            }

            100% {
                transform: rotate(-2deg)
            }
        }

        div.da-error-wrapper .da-error-code {
            width: 285px;
            height: 170px;
            padding: 127px 16px 0 16px;
            position: relative;
            margin: auto;
            margin-bottom: 20px;
            z-index: 5;
            line-height: 1;
            font-size: 32px;
            text-align: center;
            background: url("img/error-hanger.png") no-repeat center center;
            -webkit-transform-origin: center top;
            -moz-transform-origin: center top;
            transform-origin: center top;
            -webkit-animation: error-swing infinite 2s ease-in-out alternate;
            -moz-animation: error-swing infinite 2s ease-in-out alternate;
            animation: error-swing infinite 2s ease-in-out alternate
        }

        div.da-error-wrapper .da-error-code .tip {
            padding-top: 2px;
            color: #e15656;
        }

        div.da-error-wrapper .da-error-code .tip2 {
            padding-top: 15px;
        }

        div.da-error-wrapper .da-error-code .tip3 {
            padding-top: 20px;
            font-size: 16px;
            color: #e15656;
        }

        div.da-error-wrapper .da-error-pin {
            width: 38px;
            height: 38px;
            display: block;
            margin: auto;
            margin-bottom: -27px;
            z-index: 10;
            position: relative;
            background: url("img/error-pin.png") no-repeat center center
        }

        p {
            margin: 0;
            padding: 0;
        }
    </style>
    <title>错误页面</title>
</head>

<body>
<div class="da-wrapper">
    <div class="da-content">
        <div class="da-container clearfix">
            <div class="da-error-wrapper" style="text-align: center">
                <div class="da-error-pin"></div>
                <div class="da-error-code">
                    <p class="tip">STATUS:401</p>
                    <p class="tip2">您无权访问该页面</p>
                    <p class="tip3">you are not authorized to access this page.</p>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>