# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

server.type=staging
server.port=8099
app.name=mimonitor

#area = china
# dubbo
dubbo.protocol.port=20886
dubbo.registry.address=nacos://nacos:80
dubbo.group=
dubbo.group.miline=staging
dubbo.group.heraapp=open
dubbo.group.alert=opensource-outer
dubbo.group.tpc=staging-open

log.path=/home/<USER>

nacos.config.addrs=nacos:80
app.nacos=nacos:80

resttemplate.connection.timeout=10000
resttemplate.read.timeout=10000

dev.mode=false
inner.auth=false

service.selector.property=outer
metric.detail.datasource.property=es