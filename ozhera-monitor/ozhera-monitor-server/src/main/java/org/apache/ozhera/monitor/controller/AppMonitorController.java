/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.ozhera.monitor.controller;

import com.google.common.collect.Lists;
import org.apache.ozhera.app.api.model.HeraAppBaseInfoModel;
import org.apache.ozhera.app.api.model.HeraAppRoleModel;
import org.apache.ozhera.monitor.bo.AppType;
import org.apache.ozhera.monitor.bo.Pair;
import org.apache.ozhera.monitor.dao.HeraAppRoleDao;
import org.apache.ozhera.monitor.dao.model.AlarmHealthQuery;
import org.apache.ozhera.monitor.dao.model.AppMonitor;
import org.apache.ozhera.monitor.result.ErrorCode;
import org.apache.ozhera.monitor.result.Result;
import org.apache.ozhera.monitor.service.AppMonitorService;
import org.apache.ozhera.monitor.service.ComputeTimerService;
import org.apache.ozhera.monitor.service.HeraBaseInfoService;
import org.apache.ozhera.monitor.service.api.AppMonitorServiceExtension;
import org.apache.ozhera.monitor.service.extension.PlatFormTypeExtensionService;
import org.apache.ozhera.monitor.service.model.*;
import org.apache.ozhera.monitor.service.model.redis.AppAlarmData;
import com.xiaomi.mone.tpc.login.util.UserUtil;
import com.xiaomi.mone.tpc.login.vo.AuthUserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
public class AppMonitorController {

    @Autowired
    AppMonitorService appMonitorService;
    @Autowired
    ComputeTimerService computeTimerService;
    @Autowired
    HeraBaseInfoService heraBaseInfoService;

    @Autowired
    AppMonitorServiceExtension appMonitorServiceExtension;

    @Autowired
    private PlatFormTypeExtensionService platFormTypeExtensionService;

//    @ResponseBody
//    @PostMapping("/mimonitor/capacityAdjustRecord")
//    public Result selectAppAlarmHealth(@RequestBody CapacityAdjustRecordRequest request){
//        return capacityService.listCapacityAdjustRecord(request);
//    }

    @GetMapping("/mimonitor/resourceUsage")
    public Result getResourceUsageUrl(Integer appId, String appName) {
        return appMonitorServiceExtension.getResourceUsageUrl(appId, appName);
    }

    @GetMapping("/mimonitor/resourceUsagek8s")
    public Result resourceUsagek8s(Integer appId, String appName) {
        return appMonitorService.getResourceUsageUrlForK8s(appId, appName);
    }

    @ResponseBody
    @GetMapping("/mimonitor/getAppType")
    public Result getAppType(Integer id) {

        HeraAppBaseInfoModel baseInfoModel = heraBaseInfoService.getById(id);

        log.info("getAppType id : {},result :{}", id, baseInfoModel);
        Map<String, Integer> map = new HashMap<>();

        map.put("type", baseInfoModel == null ? AppType.businessType.getCode() : baseInfoModel.getAppType());

        return Result.success(map);
    }

    @Autowired
    HeraAppRoleDao heraAppRoleDao;

    @ResponseBody
    @GetMapping("/mimonitor/addHeraRoleM")
    public Result addRoleByAppIdAndPlat(String appId, Integer plat, String user) {

        HeraAppRoleModel role = new HeraAppRoleModel();
        role.setAppId(appId);
        role.setAppPlatform(plat);
        role.setUser(user);
        role.setStatus(1);
        role.setRole(0);
        role.setCreateTime(new Date());
        role.setUpdateTime(new Date());

        return heraBaseInfoService.addRole(role);
    }

    @ResponseBody
    @GetMapping("/mimonitor/addHeraRole")
    public Result addHeraRole(HeraAppRoleModel role) {

        return heraBaseInfoService.addRole(role);

    }

    @ResponseBody
    @GetMapping("/mimonitor/delHeraRole")
    public Result delHeraRole(Integer id) {

        return heraBaseInfoService.delRole(id);

    }

    @ResponseBody
    @GetMapping("/mimonitor/queryHeraRole")
    public Result queryHeraRole(HeraAppRoleQuery query) {

        log.info("queryHeraRole query:{}", query);
        return heraBaseInfoService.queryRole(query.getModel(), query.getPage(), query.getPageSize());
    }

    @ResponseBody
    @GetMapping("/mimonitor/getAppTypeByName")
    public Result getAppType(Integer projectId, String projectName) {

        HeraAppBaseInfoModel byBindIdAndName = heraBaseInfoService.getByBindIdAndName(String.valueOf(projectId), projectName);

        log.info("getAppType projectId : {},projectName{},result :{}", projectId, projectName, byBindIdAndName);
        Map<String, Integer> map = new HashMap<>();

        map.put("type", byBindIdAndName == null ? AppType.businessType.getCode() : byBindIdAndName.getAppType());

        return Result.success(map);
    }

    @ResponseBody
    @PostMapping("/mimonitor/appAlarmHealth")
    public Result selectAppAlarmHealth(HttpServletRequest request, @RequestBody AlarmHealthQuery param) {
        log.info("AppMonitorController.selectAppAlarmHealth param : {}", param);
        AuthUserVo userInfo = UserUtil.getUser();
        if (userInfo == null) {
            log.info("AppMonitorController.selectAppAlarmHealth request info error no user info found! ");
            return Result.fail(ErrorCode.unknownError);
        }

        param.setOwner(userInfo.genFullAccount());

        return appMonitorService.selectAppAlarmHealth(param);
    }

    @ResponseBody
    @PostMapping("/mimonitor/statistics")
    public Result<List<AppAlarmData>> getProjectStatistics(HttpServletRequest request, @RequestBody AppMonitorRequest param) {
        log.info("AppMonitorController.getProjectStatistics param : {}", param);
        if (param.getDuration() <= 0 || CollectionUtils.isEmpty(param.getProjectList())) {
            log.error("AppMonitorController.getProjectStatistics error! invalid param! param : {}", param);
            return Result.fail(ErrorCode.invalidParamError);
        }
        return computeTimerService.getProjectStatistics(param);
    }

    @ResponseBody
    @PostMapping("/mimonitor/titlenum/statistics")
    public Result<AppAlarmData> titlenumStatistics(HttpServletRequest request, @RequestBody AppMonitorRequest param) {

        log.info("AppMonitorController.titlenumStatistics param : {}", param);

        if (CollectionUtils.isEmpty(param.getProjectList()) || param.getStartTime() == null || param.getEndTime() == null) {
            log.error("AppMonitorController.titlenumStatistics error! invalid param! param : {}", param);
            return Result.fail(ErrorCode.invalidParamError);
        }

        AppAlarmData appAlarmData = computeTimerService.countAppMetricData(param);

        log.info("AppMonitorController.titlenumStatistics param : {},result : {}", param, appAlarmData);

        return Result.success(appAlarmData);
    }

    @ResponseBody
    @PostMapping("/mimonitor/heraApps")
    public Result<PageData> getHeraApps(HttpServletRequest request, @RequestBody HeraAppBaseQuery query) {

        if (query == null) {
            log.error("AppMonitorController.getHeraApps error! invalid param! param : {}", query);
            return Result.fail(ErrorCode.invalidParamError);
        }

        log.info("AppMonitorController.getHeraApps param : {}", query);


        AuthUserVo userInfo = UserUtil.getUser();
        if (userInfo == null) {
            log.info("AppMonitorController.getHeraApps no user info found! param : {} ", query);
            return Result.fail(ErrorCode.unknownError);
        }
        query.setParticipant(userInfo.genFullAccount());

        return heraBaseInfoService.queryByParticipant(query);

    }

    @ResponseBody
    @PostMapping("/mimonitor/getProjects")
    public Result<PageData> getProjectInfos(HttpServletRequest request, @RequestBody AppMonitorRequest param) {
        log.info("AppMonitorController.getProjectInfos param : {}", param);

        if (param == null || param.getViewType() == null) {
            log.error("AppMonitorController.getProjectInfos error! invalid param! param : {}", param);
            return Result.fail(ErrorCode.invalidParamError);
        }

        if (param.getArea() == null) {
            log.error("AppMonitorController.getProjectInfos error! no area param! param : {}", param);
            return Result.fail(ErrorCode.invalidParamError);
        }

        AuthUserVo userInfo = UserUtil.getUser();
        if (userInfo == null) {
            log.info("AppMonitorController.getProjectInfos for user request info error no user info found! param : {} ", param);
            return Result.fail(ErrorCode.unknownError);
        }

        /**
         * No longer distinguish between regions and whether or not to participate in roles,
         * replaced by full application query, and adapted to the original parameter type
         */
        return appMonitorService.getProjectInfos(userInfo.genFullAccount(), param.getAppName(), param.getPage(), param.getPageSize());

    }

    @ResponseBody
    @GetMapping("/mimonitor/getMyProjectIds")
    public Result<PageData> getMyProjectIds(HttpServletRequest request, Integer area) {

        AuthUserVo userInfo = UserUtil.getUser();
        if (userInfo == null) {
            log.info("AppMonitorController.getMyProjectIds request info error no user info found! ");
            return Result.fail(ErrorCode.unknownError);
        }

        Result<PageData> result = null;

        result = appMonitorService.getProjectInfos(userInfo.genFullAccount(), null, 1, Integer.MAX_VALUE);

        log.debug("getMyProjectIds,area:{},result:{}", area, result);

        if (ErrorCode.success.getCode() != result.getCode()) {
            return Result.fail(ErrorCode.unknownError);
        }

        List<ProjectInfo> list = (List<ProjectInfo>) result.getData().getList();

        PageData<Object> objectPageData = new PageData<>();

        if (CollectionUtils.isEmpty(list)) {
            objectPageData.setList(Lists.newArrayList());
            return Result.success(objectPageData);
        }

        List<Long> projectIds = list.stream().map(it -> {
            return it.getId();
        }).collect(Collectors.toList());


        objectPageData.setList(projectIds);
        return Result.success(objectPageData);
    }

    @PostMapping("/mimonitor/listApp")
    public Result<PageData<List<AppMonitor>>> listMyApp(HttpServletRequest request, @RequestBody AppMonitorRequest param) {

        try {
            if (param.getPageSize() == null) {
                //The default maximum display is 1000
                param.setPageSize(1000);
            }
            log.info("AppMonitorController.listApp param : {} ", param);

            AuthUserVo userInfo = UserUtil.getUser();
            if (userInfo == null) {
                log.info("AppMonitorController.listApp request info error no user info found! param : {} ", param);
                return Result.fail(ErrorCode.unknownError);
            }

            String user = userInfo.genFullAccount();

            log.info("AppMonitorController.listApp param : {} ,user : {}", param, user);

            if (param.getViewType() == null) {
                if (param.getDistinct() != null && param.getDistinct() == 1) {
                    return appMonitorService.listAppDistinct(user, param.getAppName(), param.getPage(), param.getPageSize());
                }
                return appMonitorService.listApp(param.getAppName(), user, param.getPage(), param.getPageSize());
            }

            //Specifies to query the apps I follow, and returns a list of apps I follow!
            if (param.getViewType() != null && param.getViewType().intValue() == 1) {
                return appMonitorService.listMyCareApp(param.getAppName(), user, param.getPage(), param.getPageSize());
            }

            AppMonitor appMonitor = new AppMonitor();
            appMonitor.setProjectName(param.getAppName());
            appMonitor.setAppSource(param.getPlatFormType());

            return appMonitorService.listMyApp(appMonitor, user, param.getPage(), param.getPageSize());
        } catch (Exception e) {
            log.error("AppMonitorController.listApp param : {} ,exception :{}", param, e.getMessage(), e);
            return Result.fail(ErrorCode.unknownError);
        }
    }

    @PostMapping("/mimonitor/my_and_care_app_list")
    public Result<PageData<List<AppMonitor>>> myAndCareAppList(HttpServletRequest request, @RequestBody AppMonitorRequest param) {
        try {
            param.qryInit();
            log.info("AppMonitorController.myAndCareAppList param : {} ", param);
            AuthUserVo userInfo = UserUtil.getUser();
            if (userInfo == null) {
                log.info("AppMonitorController.myAndCareAppList request info error no user info found! param : {} ", param);
                return Result.fail(ErrorCode.unknownError);
            }
            String user = userInfo.genFullAccount();
            log.info("AppMonitorController.myAndCareAppList param : {} ,user : {}", param, user);
            return appMonitorService.myAndCareAppList(user, param);
        } catch (Exception e) {
            log.error("AppMonitorController.myAndCareAppList param : {} ,exception :{}", param, e.getMessage(), e);
            return Result.fail(ErrorCode.unknownError);
        }
    }

    @PostMapping("/mimonitor/addApp")
    public Result<String> addApp(HttpServletRequest request, @RequestBody List<AppMonitorModel> params) {

        log.info("AppMonitorController.addApp param : {} ", params);

        AuthUserVo userInfo = UserUtil.getUser();
        if (userInfo == null) {
            log.info("AppMonitorController.addApp request info error no user info found! param : {} ", params);
            return Result.fail(ErrorCode.unknownError);
        }
        String user = userInfo.genFullAccount();

        // Only one entry is allowed (single application)
        AppMonitorModel param = params.get(0);

        log.info("AppMonitorController.addApp param : {} ,user : {}", param, user);
        Result<String> result = appMonitorService.createWithBaseInfo(param, user);
        log.info("AppMonitorController.addApp param : {} ,user : {} , result : {}", param, user, result);
        return result;

    }

    @GetMapping("/mimonitor/delApp")
    public Result<String> delApp(HttpServletRequest request, Integer id) {

        log.info("AppMonitorController.addApp id : {} ", id);
        return appMonitorService.delete(id);
    }

    @GetMapping("/mimonitor/delAppByProjectId")
    public Result<String> delAppByProjectId(HttpServletRequest request, Integer projectId, Integer appSource) {

        log.info("AppMonitorController.delAppByProjectId projectId : {} ,appSource : {}", projectId, appSource);
        AuthUserVo userInfo = UserUtil.getUser();
        if (userInfo == null) {
            log.info("AppMonitorController.delAppByProjectId request error no user info found! projectId : {} ,appSource : {}", projectId, appSource);
            return Result.fail(ErrorCode.INVALID_USER);
        }

        return appMonitorService.deleteByUser(projectId, appSource, userInfo.genFullAccount());
    }


    @GetMapping("/mimonitor/platFormList")
    public Result<List<Pair>> platFormList(HttpServletRequest request) {
        return Result.success(platFormTypeExtensionService.getPlatFormTypeDescList());
    }

    @GetMapping("/api-manual/deleteHeraApp")
    public Result<String> deleteHeraApp(HttpServletRequest request, Integer id) {
        AuthUserVo userInfo = UserUtil.getUser();
        if (userInfo == null) {
            log.info("AppMonitorController.deleteHeraApp request error no user info found! id : {}", id);
            return Result.fail(ErrorCode.INVALID_USER);
        }

        log.info("AppMonitorController.deleteHeraApp id : {}", id);

        if (!userInfo.genFullAccount().equals("gaoxihui")) {
            return Result.fail(ErrorCode.NoOperPermission);
        }

        heraBaseInfoService.deleAppById(id);

        return Result.success("sucess");
    }

    @GetMapping("/mimonitor/appMembers")
    public Result<List<String>> appMembers(HttpServletRequest request, String appId, Integer platForm) {
        AuthUserVo userInfo = UserUtil.getUser();
        if (userInfo == null) {
            log.info("AppMonitorController.appMembers request error no user info found! appId : {}", appId);
            return Result.fail(ErrorCode.INVALID_USER);
        }


        return heraBaseInfoService.getAppMembersByAppId(appId, platForm, userInfo.genFullAccount());
    }


    @GetMapping("/mimonitor/appTypeList")
    public Result<List<Pair>> appTypeList(HttpServletRequest request) {

        return Result.success(AppType.getCodeDescList());
    }

    @GetMapping("/mimonitor/washBaseId")
    public Result washBaseId(HttpServletRequest request) {

        appMonitorService.washBaseId();
        return Result.success("washBaseId OOOK!");
    }

    @GetMapping("/mimonitor/grafanaInterfaceList")
    public Result grafanaInterfaceList() {
        return appMonitorService.grafanaInterfaceList();
    }

    @GetMapping("/mimonitor/listapp/iamId")
    public Result listAppByIAMId(Integer iamId, Integer iamType){
        log.info("AppMonitorController.listAppByIAMId param : iamId : "+iamId+" iamType : "+iamType);
        AuthUserVo userInfo = UserUtil.getUser();
        if (userInfo == null) {
            log.info("AppMonitorController.listAppByIAMId request info error no user info found! ");
            return Result.fail(ErrorCode.unknownError);
        }

       String userName = userInfo.genFullAccount();
        return appMonitorService.selectByIAMId(iamId,iamType,userName);
    }

    //Get the historical instance of the application, the time is in ms unit
    @GetMapping("/mimonitor/historyInstance")
    public Result historyInstance(String application,String startTime,String endTime){
        //Parameter check
        if (StringUtils.isBlank(application) || StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)){
            return Result.fail(ErrorCode.invalidParamError);
        }
        log.info("AppMonitorController.historyInstance param : application : "+application+" startTime : "+startTime+" endTime : "+endTime);
        Long start = Long.parseLong(startTime) / 1000;
        Long end = Long.parseLong(endTime) / 1000;
        if (start >= end) {
            return Result.fail(ErrorCode.invalidParamError);
        }
        //underscore
        application = application.replace("-","_");
        Result result = appMonitorService.historyInstance(application, start, end);
        return result;
    }
}
