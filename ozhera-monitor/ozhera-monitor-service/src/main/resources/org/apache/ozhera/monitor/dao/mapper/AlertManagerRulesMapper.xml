<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.ozhera.monitor.dao.mapper.AlertManagerRulesMapper">
  <resultMap id="BaseResultMap" type="org.apache.ozhera.monitor.dao.model.AlertManagerRules">
    <id column="rule_id" jdbcType="INTEGER" property="ruleId" />
    <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
    <result column="rule_fn" jdbcType="VARCHAR" property="ruleFn" />
    <result column="rule_interval" jdbcType="INTEGER" property="ruleInterval" />
    <result column="rule_alert" jdbcType="VARCHAR" property="ruleAlert" />
    <result column="rule_for" jdbcType="VARCHAR" property="ruleFor" />
    <result column="rule_labels" jdbcType="VARCHAR" property="ruleLabels" />
    <result column="principal" jdbcType="VARCHAR" property="principal" />
    <result column="create_time" jdbcType="DATE" property="createTime" />
    <result column="update_time" jdbcType="DATE" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="org.apache.ozhera.monitor.dao.model.AlertManagerRules">
    <result column="rule_expr" jdbcType="LONGVARCHAR" property="ruleExpr" />
    <result column="rule_annotations" jdbcType="LONGVARCHAR" property="ruleAnnotations" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    rule_id, rule_name, rule_fn, rule_interval, rule_alert, rule_for, rule_labels, principal, 
    create_time, update_time
  </sql>
  <sql id="Blob_Column_List">
    rule_expr, rule_annotations
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="org.apache.ozhera.monitor.dao.model.AlertManagerRulesExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from rules
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null and limit >= 0">
      limit #{offset} , #{limit}
    </if>
  </select>
  <select id="selectByExample" parameterType="org.apache.ozhera.monitor.dao.model.AlertManagerRulesExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from rules
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null and limit >= 0">
      limit #{offset} , #{limit}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from rules
    where rule_id = #{ruleId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from rules
    where rule_id = #{ruleId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="org.apache.ozhera.monitor.dao.model.AlertManagerRulesExample">
    delete from rules
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="org.apache.ozhera.monitor.dao.model.AlertManagerRules">
    <selectKey keyProperty="ruleId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into rules (rule_name, rule_fn, rule_interval, 
      rule_alert, rule_for, rule_labels, 
      principal, create_time, update_time, 
      rule_expr, rule_annotations)
    values (#{ruleName,jdbcType=VARCHAR}, #{ruleFn,jdbcType=VARCHAR}, #{ruleInterval,jdbcType=INTEGER}, 
      #{ruleAlert,jdbcType=VARCHAR}, #{ruleFor,jdbcType=VARCHAR}, #{ruleLabels,jdbcType=VARCHAR}, 
      #{principal,jdbcType=VARCHAR}, #{createTime,jdbcType=DATE}, #{updateTime,jdbcType=DATE}, 
      #{ruleExpr,jdbcType=LONGVARCHAR}, #{ruleAnnotations,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="org.apache.ozhera.monitor.dao.model.AlertManagerRules">
    <selectKey keyProperty="ruleId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into rules
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ruleName != null">
        rule_name,
      </if>
      <if test="ruleFn != null">
        rule_fn,
      </if>
      <if test="ruleInterval != null">
        rule_interval,
      </if>
      <if test="ruleAlert != null">
        rule_alert,
      </if>
      <if test="ruleFor != null">
        rule_for,
      </if>
      <if test="ruleLabels != null">
        rule_labels,
      </if>
      <if test="principal != null">
        principal,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="ruleExpr != null">
        rule_expr,
      </if>
      <if test="ruleAnnotations != null">
        rule_annotations,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ruleName != null">
        #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="ruleFn != null">
        #{ruleFn,jdbcType=VARCHAR},
      </if>
      <if test="ruleInterval != null">
        #{ruleInterval,jdbcType=INTEGER},
      </if>
      <if test="ruleAlert != null">
        #{ruleAlert,jdbcType=VARCHAR},
      </if>
      <if test="ruleFor != null">
        #{ruleFor,jdbcType=VARCHAR},
      </if>
      <if test="ruleLabels != null">
        #{ruleLabels,jdbcType=VARCHAR},
      </if>
      <if test="principal != null">
        #{principal,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=DATE},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=DATE},
      </if>
      <if test="ruleExpr != null">
        #{ruleExpr,jdbcType=LONGVARCHAR},
      </if>
      <if test="ruleAnnotations != null">
        #{ruleAnnotations,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="org.apache.ozhera.monitor.dao.model.AlertManagerRulesExample" resultType="java.lang.Long">
    select count(*) from rules
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update rules
    <set>
      <if test="record.ruleId != null">
        rule_id = #{record.ruleId,jdbcType=INTEGER},
      </if>
      <if test="record.ruleName != null">
        rule_name = #{record.ruleName,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleFn != null">
        rule_fn = #{record.ruleFn,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleInterval != null">
        rule_interval = #{record.ruleInterval,jdbcType=INTEGER},
      </if>
      <if test="record.ruleAlert != null">
        rule_alert = #{record.ruleAlert,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleFor != null">
        rule_for = #{record.ruleFor,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleLabels != null">
        rule_labels = #{record.ruleLabels,jdbcType=VARCHAR},
      </if>
      <if test="record.principal != null">
        principal = #{record.principal,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=DATE},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=DATE},
      </if>
      <if test="record.ruleExpr != null">
        rule_expr = #{record.ruleExpr,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.ruleAnnotations != null">
        rule_annotations = #{record.ruleAnnotations,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update rules
    set rule_id = #{record.ruleId,jdbcType=INTEGER},
      rule_name = #{record.ruleName,jdbcType=VARCHAR},
      rule_fn = #{record.ruleFn,jdbcType=VARCHAR},
      rule_interval = #{record.ruleInterval,jdbcType=INTEGER},
      rule_alert = #{record.ruleAlert,jdbcType=VARCHAR},
      rule_for = #{record.ruleFor,jdbcType=VARCHAR},
      rule_labels = #{record.ruleLabels,jdbcType=VARCHAR},
      principal = #{record.principal,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=DATE},
      update_time = #{record.updateTime,jdbcType=DATE},
      rule_expr = #{record.ruleExpr,jdbcType=LONGVARCHAR},
      rule_annotations = #{record.ruleAnnotations,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update rules
    set rule_id = #{record.ruleId,jdbcType=INTEGER},
      rule_name = #{record.ruleName,jdbcType=VARCHAR},
      rule_fn = #{record.ruleFn,jdbcType=VARCHAR},
      rule_interval = #{record.ruleInterval,jdbcType=INTEGER},
      rule_alert = #{record.ruleAlert,jdbcType=VARCHAR},
      rule_for = #{record.ruleFor,jdbcType=VARCHAR},
      rule_labels = #{record.ruleLabels,jdbcType=VARCHAR},
      principal = #{record.principal,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=DATE},
      update_time = #{record.updateTime,jdbcType=DATE}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="org.apache.ozhera.monitor.dao.model.AlertManagerRules">
    update rules
    <set>
      <if test="ruleName != null">
        rule_name = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="ruleFn != null">
        rule_fn = #{ruleFn,jdbcType=VARCHAR},
      </if>
      <if test="ruleInterval != null">
        rule_interval = #{ruleInterval,jdbcType=INTEGER},
      </if>
      <if test="ruleAlert != null">
        rule_alert = #{ruleAlert,jdbcType=VARCHAR},
      </if>
      <if test="ruleFor != null">
        rule_for = #{ruleFor,jdbcType=VARCHAR},
      </if>
      <if test="ruleLabels != null">
        rule_labels = #{ruleLabels,jdbcType=VARCHAR},
      </if>
      <if test="principal != null">
        principal = #{principal,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=DATE},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=DATE},
      </if>
      <if test="ruleExpr != null">
        rule_expr = #{ruleExpr,jdbcType=LONGVARCHAR},
      </if>
      <if test="ruleAnnotations != null">
        rule_annotations = #{ruleAnnotations,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where rule_id = #{ruleId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="org.apache.ozhera.monitor.dao.model.AlertManagerRules">
    update rules
    set rule_name = #{ruleName,jdbcType=VARCHAR},
      rule_fn = #{ruleFn,jdbcType=VARCHAR},
      rule_interval = #{ruleInterval,jdbcType=INTEGER},
      rule_alert = #{ruleAlert,jdbcType=VARCHAR},
      rule_for = #{ruleFor,jdbcType=VARCHAR},
      rule_labels = #{ruleLabels,jdbcType=VARCHAR},
      principal = #{principal,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=DATE},
      update_time = #{updateTime,jdbcType=DATE},
      rule_expr = #{ruleExpr,jdbcType=LONGVARCHAR},
      rule_annotations = #{ruleAnnotations,jdbcType=LONGVARCHAR}
    where rule_id = #{ruleId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.apache.ozhera.monitor.dao.model.AlertManagerRules">
    update rules
    set rule_name = #{ruleName,jdbcType=VARCHAR},
      rule_fn = #{ruleFn,jdbcType=VARCHAR},
      rule_interval = #{ruleInterval,jdbcType=INTEGER},
      rule_alert = #{ruleAlert,jdbcType=VARCHAR},
      rule_for = #{ruleFor,jdbcType=VARCHAR},
      rule_labels = #{ruleLabels,jdbcType=VARCHAR},
      principal = #{principal,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=DATE},
      update_time = #{updateTime,jdbcType=DATE}
    where rule_id = #{ruleId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="rule_id" keyProperty="ruleId" parameterType="map" useGeneratedKeys="true">
    insert into rules
    (rule_name, rule_fn, rule_interval, rule_alert, rule_for, rule_labels, principal, 
      create_time, update_time, rule_expr, rule_annotations)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.ruleName,jdbcType=VARCHAR}, #{item.ruleFn,jdbcType=VARCHAR}, #{item.ruleInterval,jdbcType=INTEGER}, 
        #{item.ruleAlert,jdbcType=VARCHAR}, #{item.ruleFor,jdbcType=VARCHAR}, #{item.ruleLabels,jdbcType=VARCHAR}, 
        #{item.principal,jdbcType=VARCHAR}, #{item.createTime,jdbcType=DATE}, #{item.updateTime,jdbcType=DATE}, 
        #{item.ruleExpr,jdbcType=LONGVARCHAR}, #{item.ruleAnnotations,jdbcType=LONGVARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="rule_id" keyProperty="list.ruleId" parameterType="map" useGeneratedKeys="true">
    insert into rules (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'rule_name'.toString() == column.value">
          #{item.ruleName,jdbcType=VARCHAR}
        </if>
        <if test="'rule_fn'.toString() == column.value">
          #{item.ruleFn,jdbcType=VARCHAR}
        </if>
        <if test="'rule_interval'.toString() == column.value">
          #{item.ruleInterval,jdbcType=INTEGER}
        </if>
        <if test="'rule_alert'.toString() == column.value">
          #{item.ruleAlert,jdbcType=VARCHAR}
        </if>
        <if test="'rule_for'.toString() == column.value">
          #{item.ruleFor,jdbcType=VARCHAR}
        </if>
        <if test="'rule_labels'.toString() == column.value">
          #{item.ruleLabels,jdbcType=VARCHAR}
        </if>
        <if test="'principal'.toString() == column.value">
          #{item.principal,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=DATE}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=DATE}
        </if>
        <if test="'rule_expr'.toString() == column.value">
          #{item.ruleExpr,jdbcType=LONGVARCHAR}
        </if>
        <if test="'rule_annotations'.toString() == column.value">
          #{item.ruleAnnotations,jdbcType=LONGVARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>