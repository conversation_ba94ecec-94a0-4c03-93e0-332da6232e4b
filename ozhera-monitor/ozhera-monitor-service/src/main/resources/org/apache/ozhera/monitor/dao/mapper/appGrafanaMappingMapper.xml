<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.ozhera.monitor.dao.mapper.AppGrafanaMappingMapper">
  <resultMap id="BaseResultMap" type="org.apache.ozhera.monitor.dao.model.AppGrafanaMapping">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
    <result column="mione_env" jdbcType="VARCHAR" property="mioneEnv" />
    <result column="grafana_url" jdbcType="VARCHAR" property="grafanaUrl" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, app_name, mione_env, grafana_url, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="org.apache.ozhera.monitor.dao.model.AppGrafanaMappingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from app_grafana_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null and limit >= 0">
      limit #{offset} , #{limit}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_grafana_mapping
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from app_grafana_mapping
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="org.apache.ozhera.monitor.dao.model.AppGrafanaMappingExample">
    delete from app_grafana_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="org.apache.ozhera.monitor.dao.model.AppGrafanaMapping">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_grafana_mapping (app_name, mione_env, grafana_url, 
      create_time, update_time)
    values (#{appName,jdbcType=VARCHAR}, #{mioneEnv,jdbcType=VARCHAR}, #{grafanaUrl,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="org.apache.ozhera.monitor.dao.model.AppGrafanaMapping">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_grafana_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appName != null">
        app_name,
      </if>
      <if test="mioneEnv != null">
        mione_env,
      </if>
      <if test="grafanaUrl != null">
        grafana_url,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appName != null">
        #{appName,jdbcType=VARCHAR},
      </if>
      <if test="mioneEnv != null">
        #{mioneEnv,jdbcType=VARCHAR},
      </if>
      <if test="grafanaUrl != null">
        #{grafanaUrl,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="org.apache.ozhera.monitor.dao.model.AppGrafanaMappingExample" resultType="java.lang.Long">
    select count(*) from app_grafana_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update app_grafana_mapping
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.appName != null">
        app_name = #{record.appName,jdbcType=VARCHAR},
      </if>
      <if test="record.mioneEnv != null">
        mione_env = #{record.mioneEnv,jdbcType=VARCHAR},
      </if>
      <if test="record.grafanaUrl != null">
        grafana_url = #{record.grafanaUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update app_grafana_mapping
    set id = #{record.id,jdbcType=INTEGER},
      app_name = #{record.appName,jdbcType=VARCHAR},
      mione_env = #{record.mioneEnv,jdbcType=VARCHAR},
      grafana_url = #{record.grafanaUrl,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="org.apache.ozhera.monitor.dao.model.AppGrafanaMapping">
    update app_grafana_mapping
    <set>
      <if test="appName != null">
        app_name = #{appName,jdbcType=VARCHAR},
      </if>
      <if test="mioneEnv != null">
        mione_env = #{mioneEnv,jdbcType=VARCHAR},
      </if>
      <if test="grafanaUrl != null">
        grafana_url = #{grafanaUrl,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.apache.ozhera.monitor.dao.model.AppGrafanaMapping">
    update app_grafana_mapping
    set app_name = #{appName,jdbcType=VARCHAR},
      mione_env = #{mioneEnv,jdbcType=VARCHAR},
      grafana_url = #{grafanaUrl,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into app_grafana_mapping
    (app_name, mione_env, grafana_url, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.appName,jdbcType=VARCHAR}, #{item.mioneEnv,jdbcType=VARCHAR}, #{item.grafanaUrl,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into app_grafana_mapping (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'app_name'.toString() == column.value">
          #{item.appName,jdbcType=VARCHAR}
        </if>
        <if test="'mione_env'.toString() == column.value">
          #{item.mioneEnv,jdbcType=VARCHAR}
        </if>
        <if test="'grafana_url'.toString() == column.value">
          #{item.grafanaUrl,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>