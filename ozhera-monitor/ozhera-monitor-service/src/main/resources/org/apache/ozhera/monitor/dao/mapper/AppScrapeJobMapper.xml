<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.ozhera.monitor.dao.mapper.AppScrapeJobMapper">
  <resultMap id="BaseResultMap" type="org.apache.ozhera.monitor.dao.model.AppScrapeJob">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="iam_id" jdbcType="INTEGER" property="iamId" />
    <result column="user" jdbcType="VARCHAR" property="user" />
    <result column="message" jdbcType="VARCHAR" property="message" />
    <result column="data" jdbcType="VARCHAR" property="data" />
    <result column="job_name" jdbcType="VARCHAR" property="jobName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="job_desc" jdbcType="VARCHAR" property="jobDesc" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="org.apache.ozhera.monitor.dao.model.AppScrapeJob">
    <result column="job_json" jdbcType="LONGVARCHAR" property="jobJson" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, iam_id, user, message, data, job_name, status, job_desc, create_time, update_time
  </sql>
  <sql id="Blob_Column_List">
    job_json
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="org.apache.ozhera.monitor.dao.model.AppScrapeJobExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from app_scrape_job
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null and limit >= 0">
      limit #{offset} , #{limit}
    </if>
  </select>
  <select id="selectByExample" parameterType="org.apache.ozhera.monitor.dao.model.AppScrapeJobExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from app_scrape_job
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null and limit >= 0">
      limit #{offset} , #{limit}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from app_scrape_job
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from app_scrape_job
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="org.apache.ozhera.monitor.dao.model.AppScrapeJobExample">
    delete from app_scrape_job
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="org.apache.ozhera.monitor.dao.model.AppScrapeJob">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_scrape_job (iam_id, user, message, 
      data, job_name, status, 
      job_desc, create_time, update_time, 
      job_json)
    values (#{iamId,jdbcType=INTEGER}, #{user,jdbcType=VARCHAR}, #{message,jdbcType=VARCHAR}, 
      #{data,jdbcType=VARCHAR}, #{jobName,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{jobDesc,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{jobJson,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="org.apache.ozhera.monitor.dao.model.AppScrapeJob">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_scrape_job
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="iamId != null">
        iam_id,
      </if>
      <if test="user != null">
        user,
      </if>
      <if test="message != null">
        message,
      </if>
      <if test="data != null">
        data,
      </if>
      <if test="jobName != null">
        job_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="jobDesc != null">
        job_desc,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="jobJson != null">
        job_json,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="iamId != null">
        #{iamId,jdbcType=INTEGER},
      </if>
      <if test="user != null">
        #{user,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        #{message,jdbcType=VARCHAR},
      </if>
      <if test="data != null">
        #{data,jdbcType=VARCHAR},
      </if>
      <if test="jobName != null">
        #{jobName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="jobDesc != null">
        #{jobDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="jobJson != null">
        #{jobJson,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="org.apache.ozhera.monitor.dao.model.AppScrapeJobExample" resultType="java.lang.Long">
    select count(*) from app_scrape_job
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update app_scrape_job
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.iamId != null">
        iam_id = #{record.iamId,jdbcType=INTEGER},
      </if>
      <if test="record.user != null">
        user = #{record.user,jdbcType=VARCHAR},
      </if>
      <if test="record.message != null">
        message = #{record.message,jdbcType=VARCHAR},
      </if>
      <if test="record.data != null">
        data = #{record.data,jdbcType=VARCHAR},
      </if>
      <if test="record.jobName != null">
        job_name = #{record.jobName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.jobDesc != null">
        job_desc = #{record.jobDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.jobJson != null">
        job_json = #{record.jobJson,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update app_scrape_job
    set id = #{record.id,jdbcType=INTEGER},
      iam_id = #{record.iamId,jdbcType=INTEGER},
      user = #{record.user,jdbcType=VARCHAR},
      message = #{record.message,jdbcType=VARCHAR},
      data = #{record.data,jdbcType=VARCHAR},
      job_name = #{record.jobName,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      job_desc = #{record.jobDesc,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      job_json = #{record.jobJson,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update app_scrape_job
    set id = #{record.id,jdbcType=INTEGER},
      iam_id = #{record.iamId,jdbcType=INTEGER},
      user = #{record.user,jdbcType=VARCHAR},
      message = #{record.message,jdbcType=VARCHAR},
      data = #{record.data,jdbcType=VARCHAR},
      job_name = #{record.jobName,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      job_desc = #{record.jobDesc,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="org.apache.ozhera.monitor.dao.model.AppScrapeJob">
    update app_scrape_job
    <set>
      <if test="iamId != null">
        iam_id = #{iamId,jdbcType=INTEGER},
      </if>
      <if test="user != null">
        user = #{user,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        message = #{message,jdbcType=VARCHAR},
      </if>
      <if test="data != null">
        data = #{data,jdbcType=VARCHAR},
      </if>
      <if test="jobName != null">
        job_name = #{jobName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="jobDesc != null">
        job_desc = #{jobDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="jobJson != null">
        job_json = #{jobJson,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="org.apache.ozhera.monitor.dao.model.AppScrapeJob">
    update app_scrape_job
    set iam_id = #{iamId,jdbcType=INTEGER},
      user = #{user,jdbcType=VARCHAR},
      message = #{message,jdbcType=VARCHAR},
      data = #{data,jdbcType=VARCHAR},
      job_name = #{jobName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      job_desc = #{jobDesc,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      job_json = #{jobJson,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.apache.ozhera.monitor.dao.model.AppScrapeJob">
    update app_scrape_job
    set iam_id = #{iamId,jdbcType=INTEGER},
      user = #{user,jdbcType=VARCHAR},
      message = #{message,jdbcType=VARCHAR},
      data = #{data,jdbcType=VARCHAR},
      job_name = #{jobName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      job_desc = #{jobDesc,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into app_scrape_job
    (iam_id, user, message, data, job_name, status, job_desc, create_time, update_time, 
      job_json)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.iamId,jdbcType=INTEGER}, #{item.user,jdbcType=VARCHAR}, #{item.message,jdbcType=VARCHAR}, 
        #{item.data,jdbcType=VARCHAR}, #{item.jobName,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT}, 
        #{item.jobDesc,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.jobJson,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into app_scrape_job (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'iam_id'.toString() == column.value">
          #{item.iamId,jdbcType=INTEGER}
        </if>
        <if test="'user'.toString() == column.value">
          #{item.user,jdbcType=VARCHAR}
        </if>
        <if test="'message'.toString() == column.value">
          #{item.message,jdbcType=VARCHAR}
        </if>
        <if test="'data'.toString() == column.value">
          #{item.data,jdbcType=VARCHAR}
        </if>
        <if test="'job_name'.toString() == column.value">
          #{item.jobName,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'job_desc'.toString() == column.value">
          #{item.jobDesc,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'job_json'.toString() == column.value">
          #{item.jobJson,jdbcType=LONGVARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>