<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.ozhera.monitor.dao.mapper.AppServiceMarketMapper">
  <resultMap id="BaseResultMap" type="org.apache.ozhera.monitor.dao.model.AppServiceMarket">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="market_name" jdbcType="VARCHAR" property="marketName" />
    <result column="belong_team" jdbcType="VARCHAR" property="belongTeam" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="service_list" jdbcType="VARCHAR" property="serviceList" />
    <result column="last_updater" jdbcType="VARCHAR" property="lastUpdater" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="service_type" jdbcType="INTEGER" property="serviceType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, market_name, belong_team, creator, service_list, last_updater, remark, service_type, 
    create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="org.apache.ozhera.monitor.dao.model.AppServiceMarketExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from app_service_market
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null and limit >= 0">
      limit #{offset} , #{limit}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_service_market
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from app_service_market
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="org.apache.ozhera.monitor.dao.model.AppServiceMarketExample">
    delete from app_service_market
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="org.apache.ozhera.monitor.dao.model.AppServiceMarket">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_service_market (market_name, belong_team, creator, 
      service_list, last_updater, remark, 
      service_type, create_time, update_time
      )
    values (#{marketName,jdbcType=VARCHAR}, #{belongTeam,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{serviceList,jdbcType=VARCHAR}, #{lastUpdater,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{serviceType,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="org.apache.ozhera.monitor.dao.model.AppServiceMarket">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_service_market
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="marketName != null">
        market_name,
      </if>
      <if test="belongTeam != null">
        belong_team,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="serviceList != null">
        service_list,
      </if>
      <if test="lastUpdater != null">
        last_updater,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="serviceType != null">
        service_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="marketName != null">
        #{marketName,jdbcType=VARCHAR},
      </if>
      <if test="belongTeam != null">
        #{belongTeam,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="serviceList != null">
        #{serviceList,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdater != null">
        #{lastUpdater,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="serviceType != null">
        #{serviceType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="org.apache.ozhera.monitor.dao.model.AppServiceMarketExample" resultType="java.lang.Long">
    select count(*) from app_service_market
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update app_service_market
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.marketName != null">
        market_name = #{record.marketName,jdbcType=VARCHAR},
      </if>
      <if test="record.belongTeam != null">
        belong_team = #{record.belongTeam,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceList != null">
        service_list = #{record.serviceList,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdater != null">
        last_updater = #{record.lastUpdater,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceType != null">
        service_type = #{record.serviceType,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update app_service_market
    set id = #{record.id,jdbcType=INTEGER},
      market_name = #{record.marketName,jdbcType=VARCHAR},
      belong_team = #{record.belongTeam,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      service_list = #{record.serviceList,jdbcType=VARCHAR},
      last_updater = #{record.lastUpdater,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      service_type = #{record.serviceType,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="org.apache.ozhera.monitor.dao.model.AppServiceMarket">
    update app_service_market
    <set>
      <if test="marketName != null">
        market_name = #{marketName,jdbcType=VARCHAR},
      </if>
      <if test="belongTeam != null">
        belong_team = #{belongTeam,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="serviceList != null">
        service_list = #{serviceList,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdater != null">
        last_updater = #{lastUpdater,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="serviceType != null">
        service_type = #{serviceType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.apache.ozhera.monitor.dao.model.AppServiceMarket">
    update app_service_market
    set market_name = #{marketName,jdbcType=VARCHAR},
      belong_team = #{belongTeam,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      service_list = #{serviceList,jdbcType=VARCHAR},
      last_updater = #{lastUpdater,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      service_type = #{serviceType,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into app_service_market
    (market_name, belong_team, creator, service_list, last_updater, remark, service_type, 
      create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.marketName,jdbcType=VARCHAR}, #{item.belongTeam,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.serviceList,jdbcType=VARCHAR}, #{item.lastUpdater,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.serviceType,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into app_service_market (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'market_name'.toString() == column.value">
          #{item.marketName,jdbcType=VARCHAR}
        </if>
        <if test="'belong_team'.toString() == column.value">
          #{item.belongTeam,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'service_list'.toString() == column.value">
          #{item.serviceList,jdbcType=VARCHAR}
        </if>
        <if test="'last_updater'.toString() == column.value">
          #{item.lastUpdater,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'service_type'.toString() == column.value">
          #{item.serviceType,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>