<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.ozhera.monitor.dao.mapper.GrafanaTemplateMapper">
  <resultMap id="BaseResultMap" type="org.apache.ozhera.monitor.dao.model.GrafanaTemplate">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="platform" jdbcType="INTEGER" property="platform" />
    <result column="language" jdbcType="INTEGER" property="language" />
    <result column="app_type" jdbcType="INTEGER" property="appType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="org.apache.ozhera.monitor.dao.model.GrafanaTemplate">
    <result column="template" jdbcType="LONGVARCHAR" property="template" />
    <result column="panel_id_list" jdbcType="LONGVARCHAR" property="panelIdList" />
    <result column="url_param" jdbcType="LONGVARCHAR" property="urlParam" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, platform, language, app_type, create_time, update_time, deleted
  </sql>
  <sql id="Blob_Column_List">
    template, panel_id_list, url_param
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="org.apache.ozhera.monitor.dao.model.GrafanaTemplateExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from mione_grafana_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null and limit >= 0">
      limit #{offset} , #{limit}
    </if>
  </select>
  <select id="selectByExample" parameterType="org.apache.ozhera.monitor.dao.model.GrafanaTemplateExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mione_grafana_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null and limit >= 0">
      limit #{offset} , #{limit}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from mione_grafana_template
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from mione_grafana_template
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="org.apache.ozhera.monitor.dao.model.GrafanaTemplateExample">
    delete from mione_grafana_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="org.apache.ozhera.monitor.dao.model.GrafanaTemplate">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mione_grafana_template (name, platform, language, 
      app_type, create_time, update_time, 
      deleted, template, panel_id_list,
      url_param)
    values (#{name,jdbcType=VARCHAR}, #{platform,jdbcType=INTEGER}, #{language,jdbcType=INTEGER},
      #{appType,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{deleted,jdbcType=BIT}, #{template,jdbcType=LONGVARCHAR}, #{panelIdList,jdbcType=LONGVARCHAR},
      #{urlParam,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="org.apache.ozhera.monitor.dao.model.GrafanaTemplate">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mione_grafana_template
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="language != null">
        language,
      </if>
      <if test="appType != null">
        app_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="template != null">
        template,
      </if>
      <if test="panelIdList != null">
        panel_id_list,
      </if>
      <if test="urlParam != null">
        url_param,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=INTEGER},
      </if>
      <if test="language != null">
        #{language,jdbcType=INTEGER},
      </if>
      <if test="appType != null">
        #{appType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="template != null">
        #{template,jdbcType=LONGVARCHAR},
      </if>
      <if test="panelIdList != null">
        #{panelIdList,jdbcType=LONGVARCHAR},
      </if>
      <if test="urlParam != null">
        #{urlParam,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="org.apache.ozhera.monitor.dao.model.GrafanaTemplateExample" resultType="java.lang.Long">
    select count(*) from mione_grafana_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mione_grafana_template
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.platform != null">
        platform = #{record.platform,jdbcType=INTEGER},
      </if>
      <if test="record.language != null">
        language = #{record.language,jdbcType=INTEGER},
      </if>
      <if test="record.appType != null">
        app_type = #{record.appType,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.template != null">
        template = #{record.template,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.panelIdList != null">
        panel_id_list = #{record.panelIdList,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.urlParam != null">
        url_param = #{record.urlParam,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update mione_grafana_template
    set id = #{record.id,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      platform = #{record.platform,jdbcType=INTEGER},
      language = #{record.language,jdbcType=INTEGER},
      app_type = #{record.appType,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT},
      template = #{record.template,jdbcType=LONGVARCHAR},
      panel_id_list = #{record.panelIdList,jdbcType=LONGVARCHAR},
      url_param = #{record.urlParam,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mione_grafana_template
    set id = #{record.id,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      platform = #{record.platform,jdbcType=INTEGER},
      language = #{record.language,jdbcType=INTEGER},
      app_type = #{record.appType,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="org.apache.ozhera.monitor.dao.model.GrafanaTemplate">
    update mione_grafana_template
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=INTEGER},
      </if>
      <if test="language != null">
        language = #{language,jdbcType=INTEGER},
      </if>
      <if test="appType != null">
        app_type = #{appType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="template != null">
        template = #{template,jdbcType=LONGVARCHAR},
      </if>
      <if test="panelIdList != null">
        panel_id_list = #{panelIdList,jdbcType=LONGVARCHAR},
      </if>
      <if test="urlParam != null">
        url_param = #{urlParam,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="org.apache.ozhera.monitor.dao.model.GrafanaTemplate">
    update mione_grafana_template
    set name = #{name,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=INTEGER},
      language = #{language,jdbcType=INTEGER},
      app_type = #{appType,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      template = #{template,jdbcType=LONGVARCHAR},
      panel_id_list = #{panelIdList,jdbcType=LONGVARCHAR},
      url_param = #{urlParam,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.apache.ozhera.monitor.dao.model.GrafanaTemplate">
    update mione_grafana_template
    set name = #{name,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=INTEGER},
      language = #{language,jdbcType=INTEGER},
      app_type = #{appType,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into mione_grafana_template
    (name, platform, language, app_type, create_time, update_time, deleted, template,
      panel_id_list, url_param)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.platform,jdbcType=INTEGER}, #{item.language,jdbcType=INTEGER}, 
        #{item.appType,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.deleted,jdbcType=BIT}, #{item.template,jdbcType=LONGVARCHAR}, #{item.panelIdList,jdbcType=LONGVARCHAR},
        #{item.urlParam,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into mione_grafana_template (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'platform'.toString() == column.value">
          #{item.platform,jdbcType=INTEGER}
        </if>
        <if test="'language'.toString() == column.value">
          #{item.language,jdbcType=INTEGER}
        </if>
        <if test="'app_type'.toString() == column.value">
          #{item.appType,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=BIT}
        </if>
        <if test="'template'.toString() == column.value">
          #{item.template,jdbcType=LONGVARCHAR}
        </if>
        <if test="'panel_id_list'.toString() == column.value">
          #{item.panelIdList,jdbcType=LONGVARCHAR}
        </if>
        <if test="'url_param'.toString() == column.value">
          #{item.urlParam,jdbcType=LONGVARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>