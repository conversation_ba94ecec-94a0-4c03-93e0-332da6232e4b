<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>


    <context id="Tables" targetRuntime="MyBatis3" defaultModelType="flat">

        <plugin type="com.xiaomi.data.push.common.PagerPlugin"></plugin>
        <plugin type="com.itfsw.mybatis.generator.plugins.BatchInsertPlugin"/>
        <plugin type="com.itfsw.mybatis.generator.plugins.ModelColumnPlugin"/>
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin" />

        <!-- 注释 -->
        <commentGenerator>
            <!-- 是否生成注释代时间戳 -->
            <property name="suppressDate" value="true"/>
            <!-- 是否去除自动生成的注释 true：是 ： false:否 -->
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <jdbcConnection
                driverClass="com.mysql.jdbc.Driver"
                connectionURL="*************************************"
                userId="root"
                password="">
        </jdbcConnection>


        <!-- 非必需，类型处理器，在数据库类型和java类型之间的转换控制-->
        <!-- 默认false，把JDBC DECIMAL 和 NUMERIC 类型解析为 Integer，为 true时把JDBC DECIMAL 和
         NUMERIC 类型解析为java.math.BigDecimal -->
        <javaTypeResolver>
            <!-- 是否使用bigDecimal， false可自动转化以下类型（Long, Integer, Short, etc.） -->
            <property name="forceBigDecimals" value="false"/>
            <property name="forceIntegers" value="true"/>
        </javaTypeResolver>

        <!-- 生成实体类地址 -->
        <javaModelGenerator targetPackage="org.apache.ozhera.monitor.dao.model" targetProject="src/main/java">
        <!--<javaModelGenerator targetPackage="org.apache.ozhera.monitor.dao.model.cat" targetProject="src/main/java">-->
        <!--<javaModelGenerator targetPackage="org.apache.ozhera.push.dao.model" targetProject="/data/workspace/java/web-service/user-profile-push/src/main/java">-->
            <!-- 从数据库返回的值被清理前后的空格 -->
            <property name="trimStrings" value="true"/>
            <!-- enableSubPackages:是否让schema作为包的后缀 -->
            <property name="enableSubPackages" value="false"/>
        </javaModelGenerator>

        <!-- 生成mapper xml文件 -->
        <sqlMapGenerator targetPackage="org.apache.ozhera.monitor.mapper" targetProject="src/main/resources/">
       <!-- <sqlMapGenerator targetPackage="org.apache.ozhera.cat.dao.mapper" targetProject="src/main/resources/">-->
            <!-- enableSubPackages:是否让schema作为包的后缀 -->
            <property name="enableSubPackages" value="false"/>
        </sqlMapGenerator>

        <!-- 生成mapper xml对应Client-->
        <javaClientGenerator targetPackage="org.apache.ozhera.monitor.mapper" targetProject="src/main/java" type="XMLMAPPER">
        <!--<javaClientGenerator targetPackage="org.apache.ozhera.monitor.dao.cat" targetProject="src/main/java" type="XMLMAPPER">-->
        <!--<javaClientGenerator targetPackage="org.apache.ozhera.data.push.dao.mapper" targetProject="/data/workspace/java/web-service/user-profile-push/src/main/java" type="XMLMAPPER">-->
            <!-- enableSubPackages:是否让schema作为包的后缀 -->
            <property name="enableSubPackages" value="false"/>
        </javaClientGenerator>

        <!-- 配置表信息 -->
        <!-- schema即为数据库名 tableName为对应的数据库表 domainObjectName是要生成的实体类 enable*ByExample
            是否生成 example类 -->

        <!--<table schema="detail" tableName="conf" domainObjectName="Conf">-->
            <!--<generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--</table>-->

        <table schema="mone" tableName="app_quality_market" domainObjectName="AppQualityMarket">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>
    </context>
</generatorConfiguration>