<#--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->
{
    "dashboard":{
        "annotations":{
            "list":[
                {
                    "builtIn":1,
                    "datasource":"-- <PERSON>ana --",
                    "enable":true,
                    "hide":true,
                    "iconColor":"rgba(0, 211, 255, 1)",
                    "name":"Annotations &amp; Alerts",
                    "type":"dashboard"
                }
            ]
        },
        "description":"mione",
        "editable":true,
        "gnetId":null,
        "graphTooltip":0,
        "id":null,
        "iteration":1625575780650,
        "links":[

        ],
        "panels":[
            {
            "collapsed":false,
            "datasource":null,
            "gridPos":{
            "h":1,
            "w":24,
            "x":0,
            "y":150
            },
            "id":159,
            "panels":[

            ],
            "title":"自定义指标",
            "type":"row"
            },
            {
                "collapsed":false,
                "datasource":null,
                "gridPos":{
                    "h":1,
                    "w":24,
                    "x":0,
                    "y":0
                },
                "id":102,
                "panels":[

                ],
                "title":"应用健康度",
                "type":"row"
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":0,
                    "y":1
                },
                "hiddenSeries":false,
                "id":110,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":true,
                    "max":false,
                    "min":false,
                    "rightSide":true,
                    "show":true,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "nullPointMode":"null as zero",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":2,
                "points":false,
                "renderer":"flot",
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"sum(${env}_${serviceName}_sqlSuccessCount_total{application=\"$application\",ip=~\"$instance\"}) by (application) / sum(${env}_${serviceName}_sqlTotalCount_total{application=\"$application\",ip=~\"$instance\"}) by (application)",
                        "interval":"",
                        "legendFormat":"sql",
                        "refId":"A"
                    },
                    {
                        "exemplar":true,
                        "expr":"sum(${env}_${serviceName}_RedisSuccessCount_total{application=\"$application\",ip=~\"$instance\"}) by (application) / sum(${env}_${serviceName}_RedisTotalCount_total{application=\"$application\",ip=~\"$instance\"}) by (application)",
                        "hide":false,
                        "interval":"",
                        "legendFormat":"redis",
                        "refId":"B"
                    },
                    {
                        "exemplar":true,
                        "expr":"sum(${env}_${serviceName}_aopSuccessMethodCount_total{application=\"$application\",ip=~\"$instance\"}) by (application) / sum(${env}_${serviceName}_aopTotalMethodCount_total{application=\"$application\",ip=~\"$instance\"}) by (application)",
                        "hide":false,
                        "interval":"",
                        "legendFormat":"HTTP",
                        "refId":"C"
                    },
                    {
                        "exemplar":true,
                        "expr":"sum(${env}_${serviceName}_dubboBisSuccessCount_total{application=\"$application\",ip=~\"$instance\"}) by (application) / sum(${env}_${serviceName}_dubboBisTotalCount_total{application=\"$application\",ip=~\"$instance\"}) by (application)",
                        "hide":false,
                        "interval":"",
                        "legendFormat":"Dubbo",
                        "refId":"D"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"可用性",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:72",
                        "decimals":1,
                        "format":"percentunit",
                        "label":null,
                        "logBase":1,
                        "max":"1",
                        "min":"0",
                        "show":true
                    },
                    {
                        "$$hashKey":"object:73",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{
                        "color":{
                            "mode":"continuous-GrYlRd"
                        },
                        "custom":{
                            "align":"center",
                            "displayMode":"color-background",
                            "filterable":false
                        },
                        "mappings":[

                        ],
                        "thresholds":{
                            "mode":"absolute",
                            "steps":[
                                {
                                    "color":"green",
                                    "value":null
                                },
                                {
                                    "color":"red",
                                    "value":80
                                },
                                {
                                    "color":"#EAB839",
                                    "value":90
                                }
                            ]
                        }
                    },
                    "overrides":[
                        {
                            "matcher":{
                                "id":"byName",
                                "options":"ip"
                            },
                            "properties":[
                                {
                                    "id":"custom.width"
                                },
                                {
                                    "id":"custom.displayMode",
                                    "value":"color-text"
                                },
                                {
                                    "id":"unit"
                                },
                                {
                                    "id":"displayName",
                                    "value":"主机（点击跳转到物理机监控）"
                                },
                                {
                                    "id":"links",
                                    "value":[
                                        {
                                            "targetBlank":true,
                                            "title":"跳转到物理机监控",
                                            "url":"${hostUrl}"
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            "matcher":{
                                "id":"byName",
                                "options":"Value #A"
                            },
                            "properties":[
                                {
                                    "id":"displayName",
                                    "value":"状态"
                                },
                                {
                                    "id":"custom.displayMode",
                                    "value":"color-background"
                                },
                                {
                                    "id":"color",
                                    "value":{
                                        "fixedColor":"green",
                                        "mode":"thresholds"
                                    }
                                },
                                {
                                    "id":"noValue",
                                    "value":"宕机"
                                },
                                {
                                    "id": "mappings",
                                    "value": [
                                    {
                                        "from": "",
                                        "id": 1,
                                        "text": "宕机",
                                        "to": "",
                                        "type": 1,
                                        "value": "0"
                                    },
                                    {
                                        "from": "1",
                                        "id": 2,
                                        "text": "存活",
                                        "to": "99999999999998",
                                        "type": 2,
                                        "value": "1"
                                    }
                                            ]
                                },
                                {
                                    "id":"custom.displayMode",
                                    "value":"color-background"
                                },
                                {
                                    "id":"thresholds",
                                    "value":{
                                        "mode":"absolute",
                                        "steps":[
                                            {
                                                "color":"red",
                                                "value":null
                                            },
                                            {
                                                "color":"green",
                                                "value":1
                                            }
                                        ]
                                    }
                                }
                            ]
                        },
                        {
                            "matcher":{
                                "id":"byName",
                                "options":"Time"
                            },
                            "properties":[
                                {
                                    "id":"color",
                                    "value":{
                                        "mode":"fixed"
                                    }
                                }
                            ]
                        },
                        {
                            "matcher":{
                                "id":"byName",
                                "options":"Value #B"
                            },
                            "properties":[
                                {
                                    "id":"displayName",
                                    "value":"上一次检测时间"
                                },
                                {
                                    "id":"custom.displayMode",
                                    "value":"auto"
                                }
                            ]
                        },
                        {
                            "matcher":{
                                "id":"byName",
                                "options":"name"
                            },
                            "properties":[
                                {
                                    "id":"displayName",
                                    "value":"容器名（点击跳转到容器详情)"
                                },
                                {
                                    "id":"links",
                                    "value":[
                                        {
                                            "targetBlank":true,
                                            "title":"",
                                            "url":"${containerUrl}"
                                        }
                                    ]
                                },
                                {
                                    "id":"color",
                                    "value":{
                                        "fixedColor":"rgb(3, 2, 2)",
                                        "mode":"fixed"
                                    }
                                }
                            ]
                        }
                    ]
                },
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":12,
                    "y":1
                },
                "id":148,
                "options":{
                    "showHeader":true,
                    "sortBy":[
                        {
                            "desc":false,
                            "displayName":"点击ip跳转到容器监控"
                        }
                    ]
                },
                "pluginVersion":"7.5.3",
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"sum(process_uptime_seconds{application=\"$application\"}) by (ip)",
                        "format":"table",
                        "hide":false,
                        "instant":true,
                        "interval":"",
                        "legendFormat":"{{ip}}",
                        "refId":"A"
                    },
                    {
                        "exemplar":true,
                        "expr":"sum(container_last_seen{image!=\"\",ip=\"$instance\",replica=\"0\",name=~\"$containerName.*\"}) by (name)",
                        "format":"table",
                        "hide":false,
                        "instant":true,
                        "interval":"",
                        "legendFormat":"",
                        "refId":"B"
                    }
                ],
                "title":"实例列表 【主机数：$total】",
                "transformations":[
                    {
                        "id":"merge",
                        "options":{

                        }
                    },
                    {
                        "id":"filterFieldsByName",
                        "options":{
                            "include":{
                                "names":[
                                    "Time",
                                    "ip",
                                    "Value #A",
                                    "name"
                                ]
                            }
                        }
                    }
                ],
                "type":"table"
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":12,
                    "y":9
                },
                "hiddenSeries":false,
                "id":112,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":true,
                    "max":true,
                    "min":false,
                    "rightSide":true,
                    "show":true,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "nullPointMode":"null as zero",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":2,
                "points":false,
                "renderer":"flot",
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"sum(${env}_${serviceName}_aopTotalMethodCount_total{application=\"$application\",ip=~\"$instance\"}) by (ip)",
                        "interval":"",
                        "legendFormat":"{{ip}}-HTTP",
                        "refId":"A"
                    },
                    {
                        "exemplar":true,
                        "expr":"sum(${env}_${serviceName}_dubboBisTotalCount_total{application=\"$application\",ip=~\"$instance\"}) by (ip)",
                        "hide":false,
                        "interval":"",
                        "legendFormat":"{{ip}}-Dubbo consumer",
                        "refId":"B"
                    },
                    {
                        "exemplar":true,
                        "expr":"sum(${env}_${serviceName}_dubboInterfaceCalledCount_total{application=\"$application\",ip=~\"$instance\"}) by (ip)",
                        "hide":false,
                        "interval":"",
                        "legendFormat":"{{ip}}-Dubbo provider",
                        "refId":"C"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"调用总量",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:175",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:176",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "collapsed":false,
                "datasource":null,
                "gridPos":{
                    "h":1,
                    "w":24,
                    "x":0,
                    "y":17
                },
                "id":104,
                "panels":[

                ],
                "repeat":"application",
                "scopedVars":{
                    "application":{
                        "selected":true,
                        "text":"${serviceName}",
                        "value":"${serviceName}"
                    }
                },
                "title":"业务指标",
                "type":"row"
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":0,
                    "y":18
                },
                "hiddenSeries":false,
                "id":116,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":true,
                    "max":true,
                    "min":false,
                    "rightSide":true,
                    "show":true,
                    "sideWidth":250,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "nullPointMode":"null as zero",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":2,
                "points":false,
                "renderer":"flot",
                "scopedVars":{
                    "application":{
                        "selected":true,
                        "text":"${serviceName}",
                        "value":"${serviceName}"
                    }
                },
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"sum(rate(${env}_${serviceName}_aopTotalMethodCount_total{ip=~\"$instance\",application=\"$application\"}[$interval]))",
                        "instant":false,
                        "interval":"",
                        "intervalFactor":1,
                        "legendFormat":"total",
                        "refId":"A"
                    },
                    {
                        "exemplar":true,
                        "expr":"sum(rate(${env}_${serviceName}_aopTotalMethodCount_total{ip=~\"$instance\",application=\"$application\"}[$interval])) by (methodName,ip)",
                        "format":"time_series",
                        "hide":false,
                        "instant":false,
                        "interval":"",
                        "legendFormat":"{{methodName}}-{{ip}}",
                        "refId":"B"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"Http qps",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "transformations":[

                ],
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:180",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:181",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":12,
                    "y":18
                },
                "hiddenSeries":false,
                "id":118,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":true,
                    "max":true,
                    "min":false,
                    "rightSide":true,
                    "show":true,
                    "sideWidth":250,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "nullPointMode":"null as zero",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":2,
                "points":false,
                "renderer":"flot",
                "scopedVars":{
                    "application":{
                        "selected":true,
                        "text":"${serviceName}",
                        "value":"${serviceName}"
                    }
                },
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"sum(rate(${env}_${serviceName}_dubboMethodCalledCount_total{ip=~\"$instance\",application=\"$application\"}[$interval]))",
                        "interval":"",
                        "legendFormat":"total",
                        "refId":"A"
                    },
                    {
                        "exemplar":true,
                        "expr":"sum(rate(${env}_${serviceName}_dubboMethodCalledCount_total{ip=~\"$instance\",application=\"$application\"}[$interval])) by (serviceName,ip)",
                        "hide":false,
                        "interval":"",
                        "legendFormat":"{{ip}}-{{serviceName}}",
                        "refId":"B"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"Dubbo  provider qps",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:278",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:279",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":0,
                    "y":26
                },
                "hiddenSeries":false,
                "id":150,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":true,
                    "max":true,
                    "min":false,
                    "rightSide":true,
                    "show":true,
                    "sideWidth":250,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "nullPointMode":"null",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":2,
                "points":false,
                "renderer":"flot",
                "scopedVars":{
                    "application":{
                        "selected":true,
                        "text":"${serviceName}",
                        "value":"${serviceName}"
                    }
                },
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"sum(rate(${env}_${serviceName}_dubboBisTotalCount_total{application=\"$application\"}[$interval]))",
                        "interval":"",
                        "legendFormat":"total",
                        "refId":"A"
                    },
                    {
                        "exemplar":true,
                        "expr":"sum(rate(${env}_${serviceName}_dubboBisTotalCount_total{ip=~\"$instance\",application=\"$application\"}[$interval])) by (serviceName,ip)",
                        "hide":false,
                        "interval":"",
                        "legendFormat":"{{ip}}-{{serviceName}}",
                        "refId":"B"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"Dubbo Consumer qps",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:1801",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:1802",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":12,
                    "y":26
                },
                "hiddenSeries":false,
                "id":122,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":true,
                    "max":true,
                    "min":false,
                    "rightSide":true,
                    "show":true,
                    "sideWidth":250,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "nullPointMode":"null as zero",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":2,
                "points":false,
                "renderer":"flot",
                "scopedVars":{
                    "application":{
                        "selected":true,
                        "text":"${serviceName}",
                        "value":"${serviceName}"
                    }
                },
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"topk(10, sum by(serviceName,ip) (delta(${env}_${serviceName}_dubboConsumerTimeCost_sum{ip=~\"$instance\",application=\"$application\"}[$interval]) / delta(${env}_${serviceName}_dubboConsumerTimeCost_count{ip=~\"$instance\",application=\"$application\"}[$interval])))",
                        "interval":"",
                        "legendFormat":"{{ip}}-{{serviceName}}",
                        "refId":"A"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"top10 Dubbo 请求耗时",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:474",
                        "format":"ms",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:475",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":0,
                    "y":34
                },
                "hiddenSeries":false,
                "id":120,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":true,
                    "max":true,
                    "min":false,
                    "rightSide":true,
                    "show":true,
                    "sideWidth":250,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "nullPointMode":"null as zero",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":2,
                "points":false,
                "renderer":"flot",
                "scopedVars":{
                    "application":{
                        "selected":true,
                        "text":"${serviceName}",
                        "value":"${serviceName}"
                    }
                },
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"topk(10, sum by(methodName,ip) (delta(${env}_${serviceName}_aopMethodTimeCount_sum{ip=~\"$instance\",application=\"$application\"}[$interval]) / delta(${env}_${serviceName}_aopMethodTimeCount_count{ip=~\"$instance\",application=\"$application\"}[$interval])))",
                        "interval":"",
                        "legendFormat":"{{ip}}-{{methodName}}",
                        "refId":"A"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"top10 Http 请求耗时",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:376",
                        "format":"ms",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:377",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":12,
                    "y":34
                },
                "hiddenSeries":false,
                "id":126,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":true,
                    "max":true,
                    "min":false,
                    "rightSide":true,
                    "show":true,
                    "sideWidth":250,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "nullPointMode":"null as zero",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":2,
                "points":false,
                "renderer":"flot",
                "scopedVars":{
                    "application":{
                        "selected":true,
                        "text":"${serviceName}",
                        "value":"${serviceName}"
                    }
                },
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"histogram_quantile(0.99,sum(rate(${env}_${serviceName}_dubboConsumerTimeCost_bucket{ip=~\"$instance\",application=\"$application\"}[$interval])) by (le,methodName,serviceName,ip))",
                        "interval":"",
                        "legendFormat":"{{ip}}-{{serviceName}} -- {{methodName}}方法",
                        "refId":"A"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"99% Dubbo请求时间",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:762",
                        "format":"ms",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:763",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":0,
                    "y":42
                },
                "hiddenSeries":false,
                "id":124,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":true,
                    "max":true,
                    "min":false,
                    "rightSide":true,
                    "show":true,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "nullPointMode":"null as zero",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":2,
                "points":false,
                "renderer":"flot",
                "scopedVars":{
                    "application":{
                        "selected":true,
                        "text":"${serviceName}",
                        "value":"${serviceName}"
                    }
                },
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"histogram_quantile(0.99,sum(rate(${env}_${serviceName}_aopMethodTimeCount_bucket{ip=~\"$instance\",application=\"$application\"}[$interval])) by (le,methodName,ip))",
                        "interval":"",
                        "legendFormat":"{{ip}}-{{methodName}}",
                        "refId":"A"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"99% Http请求时间",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:664",
                        "format":"ms",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:665",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":12,
                    "y":42
                },
                "hiddenSeries":false,
                "id":130,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":true,
                    "max":true,
                    "min":false,
                    "rightSide":true,
                    "show":true,
                    "sideWidth":250,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "nullPointMode":"null as zero",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":2,
                "points":false,
                "renderer":"flot",
                "scopedVars":{
                    "application":{
                        "selected":true,
                        "text":"${serviceName}",
                        "value":"${serviceName}"
                    }
                },
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"rate(${env}_${serviceName}_dubboConsumerTimeCost_sum{ip=~\"$instance\",application=\"$application\"}[$interval]) / rate(${env}_${serviceName}_dubboConsumerTimeCost_count{ip=~\"$instance\"}[$interval])",
                        "interval":"",
                        "legendFormat":"{{ip}}-{{serviceName}}",
                        "refId":"A"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"Dubbo 平均请求时间",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:1418",
                        "format":"ms",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:1419",
                        "format":"ms",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":0,
                    "y":50
                },
                "hiddenSeries":false,
                "id":128,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":true,
                    "max":true,
                    "min":false,
                    "rightSide":true,
                    "show":true,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "nullPointMode":"null as zero",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":2,
                "points":false,
                "renderer":"flot",
                "scopedVars":{
                    "application":{
                        "selected":true,
                        "text":"${serviceName}",
                        "value":"${serviceName}"
                    }
                },
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"rate(${env}_${serviceName}_aopMethodTimeCount_sum{ip=~\"$instance\",application=\"$application\"}[$interval]) / rate(${env}_${serviceName}_aopMethodTimeCount_count{ip=~\"$instance\"}[$interval])",
                        "interval":"",
                        "legendFormat":"{{ip}}-{{methodName}}",
                        "refId":"A"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"Http 平均响应时间",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:1320",
                        "format":"ms",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:1321",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "collapsed":false,
                "datasource":null,
                "gridPos":{
                    "h":1,
                    "w":24,
                    "x":0,
                    "y":63
                },
                "id":106,
                "panels":[

                ],
                "title":"中间件",
                "type":"row"
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":0,
                    "y":59
                },
                "hiddenSeries":false,
                "id":132,
                "legend":{
                    "alignAsTable":true,
                    "avg":false,
                    "current":true,
                    "max":true,
                    "min":true,
                    "rightSide":true,
                    "show":true,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "nullPointMode":"null as zero",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":2,
                "points":false,
                "renderer":"flot",
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"sum(rate(${env}_${serviceName}_sqlTotalTimer_sum{ip=~\"$instance\",application=\"$application\"}[$interval])) by (dataSource) / sum(rate(${env}_${serviceName}_sqlTotalTimer_count{ip=~\"$instance\",application=\"$application\"}[$interval])) by (dataSource)",
                        "interval":"",
                        "legendFormat":"{{dataSource}}-{{sqlMethod}}",
                        "refId":"A"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"DB 方法平均响应时间",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:1516",
                        "format":"ms",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":"0",
                        "show":true
                    },
                    {
                        "$$hashKey":"object:1517",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":12,
                    "y":59
                },
                "hiddenSeries":false,
                "id":134,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":true,
                    "max":true,
                    "min":false,
                    "rightSide":true,
                    "show":true,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "nullPointMode":"null as zero",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":2,
                "points":false,
                "renderer":"flot",
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"sum(rate(${env}_${serviceName}_RedisMethodTimeCost_sum{ip=~\"$instance\",application=\"$application\"}[$interval])) by (host,port,method) / sum(rate(${env}_${serviceName}_RedisMethodTimeCost_count{ip=~\"$instance\",application=\"$application\"}[$interval])) by (host,port,method)",
                        "interval":"",
                        "legendFormat":"{{host}}:{{port}}-{{method}}",
                        "refId":"A"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"Redis方法平均响应时间",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:1614",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:1615",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":0,
                    "y":67
                },
                "hiddenSeries":false,
                "id":136,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":true,
                    "max":true,
                    "min":false,
                    "rightSide":true,
                    "show":true,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "nullPointMode":"null as zero",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":2,
                "points":false,
                "renderer":"flot",
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"histogram_quantile(0.99,sum(rate(${env}_${serviceName}_RedisMethodTimeCost_bucket{ip=~\"$instance\",application=\"$application\"}[$interval])) by (le,method,host,port))",
                        "interval":"",
                        "legendFormat":"{{host}}:{{port}}-{{method}}",
                        "refId":"A"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"99% Redis请求响应时间",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:1810",
                        "format":"ms",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:1811",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":12,
                    "y":67
                },
                "hiddenSeries":false,
                "id":138,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":true,
                    "max":true,
                    "min":false,
                    "rightSide":true,
                    "show":true,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "nullPointMode":"null as zero",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":2,
                "points":false,
                "renderer":"flot",
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"histogram_quantile(0.99,sum(rate(${env}_${serviceName}_sqlTotalTimer_bucket{ip=~\"$instance\",application=\"$application\"}[$interval])) by (le,dataSource,sqlMethod))",
                        "interval":"",
                        "legendFormat":"{{dataSource}}-{{sqlMethod}}",
                        "refId":"A"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"99% DB请求响应时间",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:1712",
                        "format":"ms",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":"0",
                        "show":true
                    },
                    {
                        "$$hashKey":"object:1713",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":0,
                    "y":75
                },
                "hiddenSeries":false,
                "id":140,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":true,
                    "max":true,
                    "min":false,
                    "rightSide":true,
                    "show":true,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "nullPointMode":"null as zero",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":2,
                "points":false,
                "renderer":"flot",
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"topk(10, sum by(method,key,host,port) (delta(${env}_${serviceName}_RedisMethodTimeCost_sum{ip=~\"$instance\",application=\"$application\"}[$interval]) / delta(${env}_${serviceName}_RedisMethodTimeCost_count{ip=~\"$instance\",application=\"$application\"}[$interval])))",
                        "interval":"",
                        "legendFormat":"{{host}}:{{port}}-{{method}} {{key}}",
                        "refId":"A"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"top10 Redis请求耗时",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:2006",
                        "format":"ms",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:2007",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":12,
                    "y":75
                },
                "hiddenSeries":false,
                "id":142,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":true,
                    "max":true,
                    "min":false,
                    "rightSide":true,
                    "show":true,
                    "sideWidth":300,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "nullPointMode":"null as zero",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":2,
                "points":false,
                "renderer":"flot",
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"topk(10, sum by(dataSource,sql) (delta(${env}_${serviceName}_sqlTotalTimer_sum{ip=~\"$instance\",application=\"$application\"}[$interval]) / delta(${env}_${serviceName}_sqlTotalTimer_count{ip=~\"$instance\",application=\"$application\"}[$interval])))",
                        "interval":"",
                        "legendFormat":"{{dataSource}}-{{sql}}",
                        "refId":"A"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"top10 DB请求耗时",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:1908",
                        "format":"ms",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":"0",
                        "show":true
                    },
                    {
                        "$$hashKey":"object:1909",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "aliasColors":{

                },
                "breakPoint":"50%",
                "cacheTimeout":null,
                "combine":{
                    "label":"Others",
                    "threshold":0
                },
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fontSize":"80%",
                "format":"short",
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":0,
                    "y":83
                },
                "id":144,
                "interval":null,
                "legend":{
                    "header":"",
                    "percentage":false,
                    "percentageDecimals":null,
                    "show":true,
                    "sideWidth":300,
                    "sort":"current",
                    "sortDesc":false,
                    "values":true,
                    "sideWidth": 250
                },
                "legendType":"Right side",
                "links":[

                ],
                "nullPointMode":"connected",
                "pieType":"pie",
                "pluginVersion":"7.5.3",
                "strokeWidth":1,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"topk(10,sum(${env}_${serviceName}_sqlTotalCount_total{ip=~\"$instance\",application=\"$application\"}) by (dataSource,sql))",
                        "format":"time_series",
                        "instant":true,
                        "interval":"",
                        "legendFormat":"{{dataSource}}-{{sql}}",
                        "refId":"A"
                    }
                ],
                "timeFrom":null,
                "timeShift":null,
                "title":"top10 DB请求语句",
                "type":"piechart",
                "valueName":"current"
            },
            {
                "aliasColors":{

                },
                "breakPoint":"50%",
                "cacheTimeout":null,
                "combine":{
                    "label":"Others",
                    "threshold":0
                },
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fontSize":"80%",
                "format":"short",
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":12,
                    "y":83
                },
                "id":146,
                "interval":null,
                "legend":{
                    "show":true,
                    "sideWidth":300,
                    "values":true
                },
                "legendType":"Right side",
                "links":[

                ],
                "nullPointMode":"connected",
                "pieType":"pie",
                "pluginVersion":"7.5.3",
                "strokeWidth":1,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"topk(10,sum(${env}_${serviceName}_RedisTotalCount_total{ip=~\"$instance\",application=\"$application\"}) by (host,port,method,key))",
                        "format":"time_series",
                        "instant":true,
                        "interval":"",
                        "legendFormat":"{{host}}:{{port}}-{{method}} {{key}}",
                        "refId":"A"
                    }
                ],
                "timeFrom":null,
                "timeShift":null,
                "title":"top10 Redis请求语句",
                "type":"piechart",
                "valueName":"current"
            },
            {
                "collapsed":false,
                "datasource":null,
                "gridPos":{
                    "h":1,
                    "w":24,
                    "x":0,
                    "y":91
                },
                "id":54,
                "panels":[

                ],
                "title":"JVM",
                "type":"row"
            },
            {
                "cacheTimeout":null,
                "colorBackground":false,
                "colorValue":true,
                "colors":[
                    "rgba(245, 54, 54, 0.9)",
                    "#5195ce",
                    "rgba(50, 172, 45, 0.97)"
                ],
                "datasource":"${dataSource}",
                "decimals":1,
                "editable":true,
                "error":false,
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "format":"s",
                "gauge":{
                    "maxValue":100,
                    "minValue":0,
                    "show":false,
                    "thresholdLabels":false,
                    "thresholdMarkers":true
                },
                "gridPos":{
                    "h":3,
                    "w":6,
                    "x":0,
                    "y":92
                },
                "height":"",
                "id":52,
                "interval":null,
                "links":[

                ],
                "mappingType":1,
                "mappingTypes":[
                    {
                        "name":"value to text",
                        "value":1
                    },
                    {
                        "name":"range to text",
                        "value":2
                    }
                ],
                "maxDataPoints":100,
                "nullPointMode":"connected",
                "nullText":null,
                "postfix":"",
                "postfixFontSize":"50%",
                "prefix":"",
                "prefixFontSize":"70%",
                "rangeMaps":[
                    {
                        "from":"null",
                        "text":"N/A",
                        "to":"null"
                    }
                ],
                "sparkline":{
                    "fillColor":"rgba(31, 118, 189, 0.18)",
                    "full":false,
                    "lineColor":"rgb(31, 120, 193)",
                    "show":false
                },
                "tableColumn":"",
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"process_uptime_seconds{application=\"$application\", ip=~\"$instance\"}",
                        "format":"time_series",
                        "interval":"",
                        "intervalFactor":2,
                        "legendFormat":"",
                        "metric":"",
                        "refId":"A",
                        "step":14400
                    }
                ],
                "thresholds":"",
                "title":"Uptime",
                "type":"singlestat",
                "valueFontSize":"80%",
                "valueMaps":[
                    {
                        "op":"=",
                        "text":"N/A",
                        "value":"null"
                    }
                ],
                "valueName":"current"
            },
            {
                "cacheTimeout":null,
                "colorBackground":false,
                "colorValue":true,
                "colors":[
                    "rgba(50, 172, 45, 0.97)",
                    "rgba(237, 129, 40, 0.89)",
                    "rgba(245, 54, 54, 0.9)"
                ],
                "datasource":"${dataSource}",
                "decimals":1,
                "editable":true,
                "error":false,
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "format":"percent",
                "gauge":{
                    "maxValue":100,
                    "minValue":0,
                    "show":true,
                    "thresholdLabels":false,
                    "thresholdMarkers":true
                },
                "gridPos":{
                    "h":6,
                    "w":5,
                    "x":6,
                    "y":92
                },
                "id":58,
                "interval":null,
                "links":[

                ],
                "mappingType":1,
                "mappingTypes":[
                    {
                        "name":"value to text",
                        "value":1
                    },
                    {
                        "name":"range to text",
                        "value":2
                    }
                ],
                "maxDataPoints":100,
                "nullPointMode":"connected",
                "nullText":null,
                "postfix":"",
                "postfixFontSize":"50%",
                "prefix":"",
                "prefixFontSize":"70%",
                "rangeMaps":[
                    {
                        "from":"null",
                        "text":"N/A",
                        "to":"null"
                    }
                ],
                "sparkline":{
                    "fillColor":"rgba(31, 118, 189, 0.18)",
                    "full":false,
                    "lineColor":"rgb(31, 120, 193)",
                    "show":false
                },
                "tableColumn":"",
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"sum(jvm_memory_used_bytes{application=\"$application\", ip=~\"$instance\", area=\"heap\"})*100/sum(jvm_memory_max_bytes{application=\"$application\",ip=~\"$instance\", area=\"heap\"})",
                        "format":"time_series",
                        "interval":"",
                        "intervalFactor":1,
                        "legendFormat":"",
                        "refId":"A",
                        "step":14400
                    }
                ],
                "thresholds":"70,90",
                "title":"Heap Used",
                "type":"singlestat",
                "valueFontSize":"70%",
                "valueMaps":[
                    {
                        "op":"=",
                        "text":"N/A",
                        "value":"null"
                    }
                ],
                "valueName":"current"
            },
            {
                "cacheTimeout":null,
                "colorBackground":false,
                "colorValue":true,
                "colors":[
                    "rgba(50, 172, 45, 0.97)",
                    "rgba(237, 129, 40, 0.89)",
                    "rgba(245, 54, 54, 0.9)"
                ],
                "datasource":"${dataSource}",
                "decimals":1,
                "editable":true,
                "error":false,
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "format":"percent",
                "gauge":{
                    "maxValue":100,
                    "minValue":0,
                    "show":true,
                    "thresholdLabels":false,
                    "thresholdMarkers":true
                },
                "gridPos":{
                    "h":6,
                    "w":5,
                    "x":11,
                    "y":92
                },
                "id":60,
                "interval":null,
                "links":[

                ],
                "mappingType":2,
                "mappingTypes":[
                    {
                        "name":"value to text",
                        "value":1
                    },
                    {
                        "name":"range to text",
                        "value":2
                    }
                ],
                "maxDataPoints":100,
                "nullPointMode":"connected",
                "nullText":null,
                "postfix":"",
                "postfixFontSize":"50%",
                "prefix":"",
                "prefixFontSize":"70%",
                "rangeMaps":[
                    {
                        "from":"null",
                        "text":"N/A",
                        "to":"null"
                    },
                    {
                        "from":"-99999999999999999999999999999999",
                        "text":"N/A",
                        "to":"0"
                    }
                ],
                "sparkline":{
                    "fillColor":"rgba(31, 118, 189, 0.18)",
                    "full":false,
                    "lineColor":"rgb(31, 120, 193)",
                    "show":false
                },
                "tableColumn":"",
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"sum(jvm_memory_used_bytes{application=\"$application\", ip=~\"$instance\", area=\"nonheap\"})*100/sum(jvm_memory_max_bytes{application=\"$application\",ip=~\"$instance\", area=\"nonheap\"})",
                        "format":"time_series",
                        "interval":"",
                        "intervalFactor":2,
                        "legendFormat":"",
                        "refId":"A",
                        "step":14400
                    }
                ],
                "thresholds":"70,90",
                "title":"Non-Heap Used",
                "type":"singlestat",
                "valueFontSize":"70%",
                "valueMaps":[
                    {
                        "op":"=",
                        "text":"N/A",
                        "value":"null"
                    },
                    {
                        "op":"=",
                        "text":"x",
                        "value":""
                    }
                ],
                "valueName":"current"
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":6,
                    "w":8,
                    "x":16,
                    "y":92
                },
                "hiddenSeries":false,
                "id":66,
                "legend":{
                    "alignAsTable":false,
                    "avg":false,
                    "current":false,
                    "max":false,
                    "min":false,
                    "rightSide":false,
                    "show":true,
                    "sideWidth":100,
                    "total":false,
                    "values":false,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "links":[

                ],
                "nullPointMode":"null",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":5,
                "points":false,
                "renderer":"flot",
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"process_files_open_files{application=\"$application\", ip=~\"$instance\"}",
                        "format":"time_series",
                        "interval":"",
                        "intervalFactor":1,
                        "legendFormat":"{{ip}}-Open Files",
                        "refId":"A"
                    },
                    {
                        "exemplar":true,
                        "expr":"process_files_max_files{application=\"$application\", ip=~\"$instance\"}",
                        "format":"time_series",
                        "interval":"",
                        "intervalFactor":1,
                        "legendFormat":"{{ip}}-Max Files",
                        "refId":"B"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"Process Open Files",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:435",
                        "format":"locale",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:436",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "cacheTimeout":null,
                "colorBackground":false,
                "colorValue":true,
                "colors":[
                    "rgba(245, 54, 54, 0.9)",
                    "#5195ce",
                    "rgba(50, 172, 45, 0.97)"
                ],
                "datasource":"${dataSource}",
                "decimals":null,
                "editable":true,
                "error":false,
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "format":"dateTimeAsIso",
                "gauge":{
                    "maxValue":100,
                    "minValue":0,
                    "show":false,
                    "thresholdLabels":false,
                    "thresholdMarkers":true
                },
                "gridPos":{
                    "h":3,
                    "w":6,
                    "x":0,
                    "y":95
                },
                "height":"",
                "id":56,
                "interval":null,
                "links":[

                ],
                "mappingType":1,
                "mappingTypes":[
                    {
                        "name":"value to text",
                        "value":1
                    },
                    {
                        "name":"range to text",
                        "value":2
                    }
                ],
                "maxDataPoints":100,
                "nullPointMode":"connected",
                "nullText":null,
                "postfix":"",
                "postfixFontSize":"50%",
                "prefix":"",
                "prefixFontSize":"70%",
                "rangeMaps":[
                    {
                        "from":"null",
                        "text":"N/A",
                        "to":"null"
                    }
                ],
                "sparkline":{
                    "fillColor":"rgba(31, 118, 189, 0.18)",
                    "full":false,
                    "lineColor":"rgb(31, 120, 193)",
                    "show":false
                },
                "tableColumn":"",
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"process_start_time_seconds{application=\"$application\", ip=~\"$instance\"}*1000",
                        "format":"time_series",
                        "interval":"",
                        "intervalFactor":2,
                        "legendFormat":"",
                        "metric":"",
                        "refId":"A",
                        "step":14400
                    }
                ],
                "thresholds":"",
                "title":"Start time",
                "type":"singlestat",
                "valueFontSize":"70%",
                "valueMaps":[
                    {
                        "op":"=",
                        "text":"N/A",
                        "value":"null"
                    }
                ],
                "valueName":"current"
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":7,
                    "w":12,
                    "x":0,
                    "y":98
                },
                "hiddenSeries":false,
                "id":95,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":true,
                    "max":true,
                    "min":true,
                    "show":true,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "links":[

                ],
                "nullPointMode":"null",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":5,
                "points":false,
                "renderer":"flot",
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"system_cpu_usage{ip=~\"$instance\", application=\"$application\"}",
                        "format":"time_series",
                        "interval":"",
                        "intervalFactor":1,
                        "legendFormat":"{{ip}}-System CPU Usage",
                        "refId":"A"
                    },
                    {
                        "exemplar":true,
                        "expr":"process_cpu_usage{ip=~\"$instance\", application=\"$application\"}",
                        "format":"time_series",
                        "interval":"",
                        "intervalFactor":1,
                        "legendFormat":"{{ip}}-Process CPU Usage",
                        "refId":"B"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"CPU Usage",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:607",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:608",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":7,
                    "w":12,
                    "x":12,
                    "y":98
                },
                "hiddenSeries":false,
                "id":96,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":true,
                    "max":true,
                    "min":true,
                    "show":true,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "links":[

                ],
                "nullPointMode":"null",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":5,
                "points":false,
                "renderer":"flot",
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"system_load_average_1m{ip=~\"$instance\", application=\"$application\"}",
                        "format":"time_series",
                        "interval":"",
                        "intervalFactor":1,
                        "legendFormat":"{{ip}}-Load Average [1m]",
                        "refId":"A"
                    },
                    {
                        "exemplar":true,
                        "expr":"system_cpu_count{ip=~\"$instance\", application=\"$application\"}",
                        "format":"time_series",
                        "interval":"",
                        "intervalFactor":1,
                        "legendFormat":"{{ip}}-CPU Core Size",
                        "refId":"B"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"Load Average",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:692",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:693",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "decimals":0,
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":0,
                    "y":105
                },
                "hiddenSeries":false,
                "id":50,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":true,
                    "max":true,
                    "min":true,
                    "show":true,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "links":[

                ],
                "nullPointMode":"null",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":5,
                "points":false,
                "renderer":"flot",
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"jvm_classes_loaded_classes{ip=~\"$instance\", application=\"$application\"}",
                        "format":"time_series",
                        "interval":"",
                        "intervalFactor":1,
                        "legendFormat":"{{ip}}-Classes Loaded",
                        "refId":"A"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"Classes Loaded",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:777",
                        "decimals":0,
                        "format":"locale",
                        "label":"",
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:778",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":12,
                    "y":105
                },
                "hiddenSeries":false,
                "id":82,
                "legend":{
                    "avg":false,
                    "current":false,
                    "max":false,
                    "min":false,
                    "show":true,
                    "total":false,
                    "values":false,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "links":[

                ],
                "nullPointMode":"null",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":5,
                "points":false,
                "renderer":"flot",
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"jvm_buffer_memory_used_bytes{ip=~\"$instance\", application=\"$application\", id=\"direct\"}",
                        "format":"time_series",
                        "interval":"",
                        "intervalFactor":1,
                        "legendFormat":"{{ip}}-Used Bytes",
                        "refId":"A"
                    },
                    {
                        "exemplar":true,
                        "expr":"jvm_buffer_total_capacity_bytes{ip=~\"$instance\", application=\"$application\", id=\"direct\"}",
                        "format":"time_series",
                        "interval":"",
                        "intervalFactor":1,
                        "legendFormat":"{{ip}}-Capacity Bytes",
                        "refId":"B"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"Direct Buffers",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:947",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:948",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":0,
                    "y":113
                },
                "hiddenSeries":false,
                "id":68,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":true,
                    "max":true,
                    "min":true,
                    "show":true,
                    "total":false,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "links":[

                ],
                "nullPointMode":"null",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":5,
                "points":false,
                "renderer":"flot",
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"jvm_threads_daemon_threads{ip=~\"$instance\", application=\"$application\"}",
                        "format":"time_series",
                        "interval":"",
                        "intervalFactor":1,
                        "legendFormat":"{{ip}}-Daemon",
                        "refId":"A"
                    },
                    {
                        "exemplar":true,
                        "expr":"jvm_threads_live_threads{ip=~\"$instance\", application=\"$application\"}",
                        "format":"time_series",
                        "interval":"",
                        "intervalFactor":1,
                        "legendFormat":"{{ip}}-Live",
                        "refId":"B"
                    },
                    {
                        "exemplar":true,
                        "expr":"jvm_threads_peak_threads{ip=~\"$instance\", application=\"$application\"}",
                        "format":"time_series",
                        "interval":"",
                        "intervalFactor":1,
                        "legendFormat":"{{ip}}-Peak",
                        "refId":"C"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"Threads",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:1032",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:1033",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":8,
                    "w":12,
                    "x":12,
                    "y":113
                },
                "hiddenSeries":false,
                "id":78,
                "legend":{
                    "avg":false,
                    "current":false,
                    "max":false,
                    "min":false,
                    "show":true,
                    "total":false,
                    "values":false,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "links":[

                ],
                "nullPointMode":"null",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":5,
                "points":false,
                "renderer":"flot",
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"irate(jvm_gc_memory_allocated_bytes_total{ip=~\"$instance\", application=\"$application\"}[5m])",
                        "format":"time_series",
                        "interval":"",
                        "intervalFactor":1,
                        "legendFormat":"{{ip}}-allocated",
                        "refId":"A"
                    },
                    {
                        "exemplar":true,
                        "expr":"irate(jvm_gc_memory_promoted_bytes_total{ip=~\"$instance\", application=\"$application\"}[5m])",
                        "format":"time_series",
                        "interval":"",
                        "intervalFactor":1,
                        "legendFormat":"{{ip}}-promoted",
                        "refId":"B"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"Memory Allocate/Promote",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:1117",
                        "format":"bytes",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:1118",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":10,
                    "w":12,
                    "x":0,
                    "y":121
                },
                "hiddenSeries":false,
                "id":74,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":false,
                    "hideEmpty":true,
                    "hideZero":true,
                    "max":true,
                    "min":true,
                    "show":true,
                    "total":true,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "links":[

                ],
                "nullPointMode":"null",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":5,
                "points":false,
                "renderer":"flot",
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"irate(jvm_gc_pause_seconds_count{ip=~\"$instance\", application=\"$application\"}[5m])",
                        "format":"time_series",
                        "interval":"",
                        "intervalFactor":1,
                        "legendFormat":"{{ip}}-{{action}} [{{cause}}]",
                        "refId":"A"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"GC Count",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:1202",
                        "format":"locale",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:1203",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            },
            {
            "aliasColors":{

            },
            "bars":false,
            "dashLength":10,
            "dashes":false,
            "datasource":null,
            "fieldConfig":{
            "defaults":{

            },
            "overrides":[

            ]
            },
            "fill":1,
            "fillGradient":0,
            "gridPos":{
            "h":8,
            "w":12,
            "x":0,
            "y":9
            },
            "hiddenSeries":false,
            "id":152,
            "legend":{
            "avg":false,
            "current":false,
            "max":false,
            "min":false,
            "show":true,
            "total":false,
            "values":false
            },
            "lines":true,
            "linewidth":1,
            "nullPointMode":"null",
            "options":{
            "alertThreshold":true
            },
            "percentage":false,
            "pluginVersion":"7.5.3",
            "pointradius":2,
            "points":false,
            "renderer":"flot",
            "seriesOverrides":[

            ],
            "spaceLength":10,
            "stack":false,
            "steppedLine":false,
            "thresholds":[

            ],
            "timeFrom":null,
            "timeRegions":[

            ],
            "timeShift":null,
            "title":"业务可用性",
            "tooltip":{
            "shared":true,
            "sort":0,
            "value_type":"individual"
            },
            "type":"graph",
            "xaxis":{
            "buckets":null,
            "mode":"time",
            "name":null,
            "show":true,
            "values":[

            ]
            },
            "yaxes":[
            {
            "format":"short",
            "label":null,
            "logBase":1,
            "max":null,
            "min":null,
            "show":true
            },
            {
            "format":"short",
            "label":null,
            "logBase":1,
            "max":null,
            "min":null,
            "show":true
            }
            ],
            "yaxis":{
            "align":false,
            "alignLevel":null
            }
            },
            {
                "aliasColors":{

                },
                "bars":false,
                "dashLength":10,
                "dashes":false,
                "datasource":"${dataSource}",
                "fieldConfig":{
                    "defaults":{

                    },
                    "overrides":[

                    ]
                },
                "fill":1,
                "fillGradient":0,
                "gridPos":{
                    "h":10,
                    "w":12,
                    "x":12,
                    "y":121
                },
                "hiddenSeries":false,
                "id":76,
                "legend":{
                    "alignAsTable":true,
                    "avg":true,
                    "current":false,
                    "hideEmpty":true,
                    "hideZero":true,
                    "max":true,
                    "min":true,
                    "show":true,
                    "total":true,
                    "values":true,
                    "sideWidth": 250
                },
                "lines":true,
                "linewidth":1,
                "links":[

                ],
                "nullPointMode":"null",
                "options":{
                    "alertThreshold":true
                },
                "percentage":false,
                "pluginVersion":"7.5.3",
                "pointradius":5,
                "points":false,
                "renderer":"flot",
                "seriesOverrides":[

                ],
                "spaceLength":10,
                "stack":false,
                "steppedLine":false,
                "targets":[
                    {
                        "exemplar":true,
                        "expr":"irate(jvm_gc_pause_seconds_sum{ip=~\"$instance\", application=\"$application\"}[5m])",
                        "format":"time_series",
                        "interval":"",
                        "intervalFactor":1,
                        "legendFormat":"{{ip}}-{{action}} [{{cause}}]",
                        "refId":"A"
                    }
                ],
                "thresholds":[

                ],
                "timeFrom":null,
                "timeRegions":[

                ],
                "timeShift":null,
                "title":"GC Stop the World Duration",
                "tooltip":{
                    "shared":true,
                    "sort":0,
                    "value_type":"individual"
                },
                "type":"graph",
                "xaxis":{
                    "buckets":null,
                    "mode":"time",
                    "name":null,
                    "show":true,
                    "values":[

                    ]
                },
                "yaxes":[
                    {
                        "$$hashKey":"object:1287",
                        "format":"s",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    },
                    {
                        "$$hashKey":"object:1288",
                        "format":"short",
                        "label":null,
                        "logBase":1,
                        "max":null,
                        "min":null,
                        "show":true
                    }
                ],
                "yaxis":{
                    "align":false,
                    "alignLevel":null
                }
            }
        ],
        "refresh":"30s",
        "schemaVersion":27,
        "style":"dark",
        "tags":[
            "templated"
        ],
        "templating":{
            "list":[
                {
                    "description":null,
                    "error":null,
                    "hide":2,
                    "label":"Application",
                    "name":"application",
                    "query":"${serviceName}",
                    "skipUrlSync":false,
                    "type":"constant"
                },
                {
                    "allValue":null,
                    "datasource":"${dataSource}",
                    "definition":"label_values(jvm_classes_loaded_classes{application=\"$application\"},ip)",
                    "description":null,
                    "error":null,
                    "hide":0,
                    "includeAll":false,
                    "label":"Instance",
                    "multi":true,
                    "name":"instance",
                    "options":[

                    ],
                    "query":{
                        "query":"label_values(jvm_classes_loaded_classes{application=\"$application\"},ip)",
                        "refId":"StandardVariableQuery"
                    },
                    "refresh":1,
                    "regex":"",
                    "skipUrlSync":false,
                    "sort":1,
                    "tagValuesQuery":"",
                    "tags":[

                    ],
                    "tagsQuery":"",
                    "type":"query",
                    "useTags":false
                },
                {
                    "allValue":null,
                    "current":{
                        "selected":false,
                        "text":"2",
                        "value":"2"
                    },
                    "datasource":"${dataSource}",
                    "definition":"query_result(count(jvm_classes_loaded_classes{application=\"$application\"}))",
                    "description":null,
                    "error":null,
                    "hide":2,
                    "includeAll":false,
                    "label":"主机数",
                    "multi":false,
                    "name":"total",
                    "options":[

                    ],
                    "query":{
                        "query":"query_result(count(jvm_classes_loaded_classes{application=\"$application\"}))",
                        "refId":"StandardVariableQuery"
                    },
                    "refresh":1,
                    "regex":"/{} (.*) .*/",
                    "skipUrlSync":false,
                    "sort":0,
                    "tagValuesQuery":"",
                    "tags":[

                    ],
                    "tagsQuery":"",
                    "type":"query",
                    "useTags":false
                },
                {
                    "allValue":null,
                    "current":{
                        "selected":false,
                        "text":"xxx",
                        "value":"xxx"
                    },
                    "datasource":"${dataSource}",
                    "definition":"label_values(ip)",
                    "description":null,
                    "error":null,
                    "hide":2,
                    "includeAll":false,
                    "label":"Ip",
                    "multi":false,
                    "name":"ip",
                    "options":[

                    ],
                    "query":{
                        "query":"label_values(ip)",
                        "refId":"StandardVariableQuery"
                    },
                    "refresh":1,
                    "regex":"\\d+\\.\\d+\\.\\d+\\.\\d+",
                    "skipUrlSync":false,
                    "sort":0,
                    "tagValuesQuery":"",
                    "tags":[

                    ],
                    "tagsQuery":"",
                    "type":"query",
                    "useTags":false
                },
                {
                    "auto":true,
                    "auto_count":30,
                    "auto_min":"10s",
                    "current":{
                        "selected":true,
                        "text":"30s",
                        "value":"30s"
                    },
                    "description":null,
                    "error":null,
                    "hide":2,
                    "label":"时间间隔",
                    "name":"interval",
                    "options":[
                        {
                            "selected":false,
                            "text":"auto",
                            "value":"$__auto_interval_interval"
                        },
                        {
                            "selected":true,
                            "text":"30s",
                            "value":"30s"
                        },
                        {
                            "selected":false,
                            "text":"1m",
                            "value":"1m"
                        },
                        {
                            "selected":false,
                            "text":"2m",
                            "value":"2m"
                        },
                        {
                            "selected":false,
                            "text":"3m",
                            "value":"3m"
                        },
                        {
                            "selected":false,
                            "text":"5m",
                            "value":"5m"
                        },
                        {
                            "selected":false,
                            "text":"10m",
                            "value":"10m"
                        },
                        {
                            "selected":false,
                            "text":"30m",
                            "value":"30m"
                        }
                    ],
                    "query":"30s,1m,2m,3m,5m,10m,30m",
                    "queryValue":"",
                    "refresh":2,
                    "skipUrlSync":false,
                    "type":"interval"
                },
                {
                "description": null,
                "error": null,
                "hide": 2,
                "label": "容器名",
                "name": "containerName",
                "query": ${containerName},
                "skipUrlSync": false,
                "type": "constant"
                }
            ]
        },
        "time":{
            "from":"now-5m",
            "to":"now"
        },
        "timepicker":{
            "refresh_intervals":[
                "5s",
                "10s",
                "30s",
                "1m",
                "5m",
                "15m",
                "30m",
                "1h",
                "2h",
                "1d"
            ],
            "time_options":[
                "5m",
                "15m",
                "1h",
                "6h",
                "12h",
                "24h",
                "2d",
                "7d",
                "30d"
            ]
        },
        "timezone":"browser",
        "title":"业务监控-${title}",
        "uid":"${uid}",
        "version":222
    },
    "overwrite":false,
    "folderId":${folderId},
    "folderUid":${folderUid}
}