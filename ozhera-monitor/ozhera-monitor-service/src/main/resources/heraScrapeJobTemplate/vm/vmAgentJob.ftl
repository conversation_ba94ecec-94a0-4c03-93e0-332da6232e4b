<#--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->
{
"region": "chn-tianjin",
"zone": "ksywq",
"env":"online",
"job_name":"mione-vm-agent",
"scrape_interval":"30s",
"scrape_timeout":"10s",
"metrics_path":"/metrics",
"honor_labels":false,
"honor_timestamps":false,
"scheme":"http",
"relabel_configs":[
{
"regex":"(.*)",
"target_label":"system",
"replacement":"mione",
"action":"replace",
"separator":";"
},
{
"source_labels":[
"__address__"
],
"regex":"(\\d+\\.\\d+\\.\\d+\\.\\d+).*",
"target_label":"ip",
"replacement":"$1",
"action":"replace"
}
],
"http_sd_configs":[
{
"url":"http://prometheus-agent-independent.ozhera-namespace/api/v1/vm/getClusterIp?name=ozhera-vmagent"
}
]
}