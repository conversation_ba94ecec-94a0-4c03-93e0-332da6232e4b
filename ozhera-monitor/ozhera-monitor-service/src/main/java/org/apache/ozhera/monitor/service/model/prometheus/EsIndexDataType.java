/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.ozhera.monitor.service.model.prometheus;

/**
 * <AUTHOR>
 * @date 2021/9/3 9:05 上午
 */
public enum EsIndexDataType {
    http,
    http_client,
    dubbo_consumer,
    dubbo_provider,
    dubbo_sla,

    grpc_client,
    grpc_server,
    thrift_client,
    thrift_server,
    apus_client,
    apus_server,
    mq_consumer,
    mq_producer,

    hbase,
    redis,
    oracle,
    mysql,
    elasticsearch;
}
