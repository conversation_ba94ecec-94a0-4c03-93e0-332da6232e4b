/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.ozhera.monitor.bo;

/**
 * <AUTHOR>
 * @date 2022/4/13
 */
public enum serviceMarketType {
    mione(0,"mione"),
    info(1,"info"),
    youpin(2,"youpin"),
    micloud_deployment(6,"deployment"),
    micloud_mice(7,"mice"),
    micloud_matrix(8,"matrix"),
    ;

    private Integer code;
    private String name;

     serviceMarketType(Integer code,String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static boolean isMarketType(Integer code){
        serviceMarketType[] values = serviceMarketType.values();
        for(serviceMarketType value : values){
            if(value.getCode().equals(code)){
                return true;
            }
        }
        return false;
    }
}
