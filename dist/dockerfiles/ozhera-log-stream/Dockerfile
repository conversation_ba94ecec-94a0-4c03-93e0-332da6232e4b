# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
FROM openjdk:21-jdk-bookworm

ENV APP_HOME /opt/app

RUN mkdir -p ${APP_HOME}

RUN echo 'Asia/Shanghai' >/etc/timezone

ARG SRC_PATH
ARG LIB_PATH
ARG APP_VERSION
COPY ${SRC_PATH}/log-stream-${APP_VERSION}.jar ${APP_HOME}/log-stream.jar

WORKDIR ${APP_HOME}

COPY ${LIB_PATH}/lib /home/<USER>/lib/
COPY ${LIB_PATH}/ext-lib /home/<USER>/lib/

CMD ["java", "--add-opens=java.base/java.util.regex=ALL-UNNAMED", "--add-opens=java.base/java.lang=ALL-UNNAMED", "--add-opens=java.base/java.lang.invoke=ALL-UNNAMED",  "--add-opens=java.base/java.util=ALL-UNNAMED", "--add-opens=java.base/sun.nio.fs=ALL-UNNAMED", "-cp", "/opt/app/log-stream.jar:/home/<USER>/lib/*", "org.apache.ozhera.log.stream.bootstrap.MiLogStreamBootstrap"]